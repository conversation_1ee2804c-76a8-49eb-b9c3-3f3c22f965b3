# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

Connecto AI Voice Receptionist Platform - A comprehensive AI-powered voice assistant system for small and medium enterprises (SMEs). The platform allows businesses to deploy customized AI voice assistants that handle incoming calls, schedule appointments, and provide business-specific information.

## Architecture

### Backend (Connector/backend/)
- **Python Flask API** with LiveKit Agents for real-time voice processing
- **OpenAI integration** for speech-to-text and text-to-speech
- **Supabase** for database and authentication
- **Business-specific prompts** in `prompt.py` for different industries (restaurants, auto service, salons, medical offices, etc.)

### Frontend (Connector/frontend/)
- **React 19** with Vite build system
- **React Router 6** for client-side routing
- **Tailwind CSS** for styling
- **Supabase Auth** for authentication
- **Axios** for API communication

### Virtual Environment (agente/)
- Python virtual environment with all backend dependencies

## Development Commands

### Backend
```bash
# Install dependencies
pip install -r Connector/backend/requirements.txt

# Start Flask API server
cd Connector/backend && python api.py

# Run voice demo
cd Connector/backend && python demo.py
# or
./Connector/backend/run_demo.sh --business-type restaurant --business-name "Demo Restaurant"

# List available business types
./Connector/backend/run_demo.sh --list-types
```

### Frontend
```bash
# Install dependencies
cd Connector/frontend && npm install

# Start development server
cd Connector/frontend && npm run dev

# Build for production
cd Connector/frontend && npm run build

# Run linting
cd Connector/frontend && npm run lint

# Preview production build
cd Connector/frontend && npm run preview
```

## Environment Configuration

### Backend (.env in Connector/backend/)
```
SUPABASE_URL=https://your-project-id.supabase.co
SUPABASE_KEY=your-supabase-api-key
OPENAI_API_KEY=your-openai-api-key
LIVEKIT_URL=your-livekit-url
LIVEKIT_API_KEY=your-livekit-api-key
LIVEKIT_API_SECRET=your-livekit-api-secret
```

### Frontend (.env.local in Connector/frontend/)
```
VITE_SUPABASE_URL=https://your-project-id.supabase.co
VITE_SUPABASE_ANON_KEY=your-supabase-anon-key
VITE_API_URL=http://localhost:5000
```

## Key Components

### Agent Core (agent.py)
Main agent implementation using LiveKit for voice processing. Handles speech input, generates responses, and manages call flow based on business type.

### API Layer (api.py)
Flask API providing endpoints for:
- User authentication and management
- Business profile management
- Agent configuration
- Call logs and statistics
- Voice preview functionality

### Database Driver (db_driver.py)
Supabase integration handling:
- User authentication and profiles
- Business registration and configuration
- Agent settings storage
- Call logging and statistics

### Prompt Templates (prompt.py)
Business-specific templates defining AI assistant behavior for different industries, including role definitions, conversation goals, and appointment handling instructions.

## Database Schema

### Public Schema
- `user_profiles`: User account information
- `restaurants`: Business profiles (used for all business types)
- `business_applications`: Links businesses to applications

### Connecto Schema
- `agent_configs`: AI voice receptionist configuration for each business
- `call_logs`: Call history and details

## Business Types Supported
Auto service, nail salons, restaurants, barbershops, medical offices, dental offices, spas, law firms, real estate offices, fitness centers, and more.

## Testing
- Voice preview functionality via API endpoints
- Demo mode for testing call handling
- Business registration flow testing