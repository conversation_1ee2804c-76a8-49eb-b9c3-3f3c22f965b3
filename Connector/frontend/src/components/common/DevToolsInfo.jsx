import React, { useState } from 'react';
import { X, ExternalLink } from 'lucide-react';

const DevToolsInfo = () => {
  const [isVisible, setIsVisible] = useState(true);

  if (!isVisible) return null;

  return (
    <div className="fixed bottom-4 right-4 bg-blue-50 border border-blue-200 rounded-lg p-4 max-w-sm shadow-lg z-50">
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <h3 className="text-sm font-medium text-blue-900 mb-2">
            React DevTools Recommended
          </h3>
          <p className="text-xs text-blue-700 mb-3">
            Install React DevTools for better debugging experience
          </p>
          <a
            href="https://react.dev/link/react-devtools"
            target="_blank"
            rel="noopener noreferrer"
            className="inline-flex items-center text-xs text-blue-600 hover:text-blue-800 underline"
          >
            Install DevTools
            <ExternalLink className="ml-1 h-3 w-3" />
          </a>
        </div>
        <button
          onClick={() => setIsVisible(false)}
          className="text-blue-400 hover:text-blue-600 ml-2"
        >
          <X className="h-4 w-4" />
        </button>
      </div>
    </div>
  );
};

export default DevToolsInfo; 