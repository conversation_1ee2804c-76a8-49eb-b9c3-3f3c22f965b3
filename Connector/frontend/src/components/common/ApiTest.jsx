import React, { useState } from 'react';
import { businessAPI, authAPI } from '../../utils/api';
import Button from './Button';
import { Card, CardHeader, CardTitle, CardContent } from './Card';

const ApiTest = () => {
  const [results, setResults] = useState({});
  const [loading, setLoading] = useState({});

  const testEndpoint = async (name, apiCall) => {
    setLoading(prev => ({ ...prev, [name]: true }));
    try {
      console.log(`🧪 Testing ${name}...`);
      const response = await apiCall();
      console.log(`✅ ${name} success:`, response);
      setResults(prev => ({
        ...prev,
        [name]: {
          success: true,
          data: response.data,
          status: response.status
        }
      }));
    } catch (error) {
      console.error(`❌ ${name} failed:`, error);
      setResults(prev => ({
        ...prev,
        [name]: {
          success: false,
          error: error.message,
          response: error.response?.data,
          status: error.response?.status
        }
      }));
    } finally {
      setLoading(prev => ({ ...prev, [name]: false }));
    }
  };

  const tests = [
    {
      name: 'Auth Check',
      test: () => testEndpoint('auth', () => authAPI.getCurrentUser())
    },
    {
      name: 'User Businesses',
      test: () => testEndpoint('businesses', () => businessAPI.getUserBusinesses())
    },
    {
      name: 'Transferable Businesses',
      test: () => testEndpoint('transferable', () => businessAPI.getTransferable())
    }
  ];

  const getAuthInfo = () => {
    const token = localStorage.getItem('access_token');
    const user = localStorage.getItem('user');
    return {
      hasToken: !!token,
      tokenLength: token?.length,
      tokenPreview: token ? `${token.substring(0, 20)}...` : null,
      hasUser: !!user,
      userInfo: user ? JSON.parse(user) : null
    };
  };

  return (
    <Card className="mt-6">
      <CardHeader>
        <CardTitle>API Debug Panel</CardTitle>
      </CardHeader>
      <CardContent>
        {/* Auth Info */}
        <div className="mb-6 p-4 bg-gray-50 rounded-lg">
          <h4 className="font-medium mb-2">Authentication Status</h4>
          <pre className="text-sm text-gray-600 overflow-auto">
            {JSON.stringify(getAuthInfo(), null, 2)}
          </pre>
        </div>

        {/* Test Buttons */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          {tests.map(({ name, test }) => (
            <Button
              key={name}
              onClick={test}
              loading={loading[name]}
              variant="outline"
              size="sm"
            >
              Test {name}
            </Button>
          ))}
        </div>

        {/* Results */}
        <div className="space-y-4">
          {Object.entries(results).map(([name, result]) => (
            <div key={name} className="border rounded-lg p-4">
              <div className="flex items-center gap-2 mb-2">
                <span className={`w-3 h-3 rounded-full ${
                  result.success ? 'bg-green-500' : 'bg-red-500'
                }`}></span>
                <h5 className="font-medium">{name}</h5>
                <span className="text-sm text-gray-500">
                  Status: {result.status || 'N/A'}
                </span>
              </div>
              <pre className="text-xs bg-gray-50 p-2 rounded overflow-auto max-h-40">
                {JSON.stringify(result, null, 2)}
              </pre>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

export default ApiTest;
