import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { 
  LayoutDashboard, 
  Building2, 
  Phone, 
  Play, 
  Settings,
  HelpCircle,
  Calendar,
  Clock,
  BarChart3,
  Users,
  Headphones,
  Plus
} from 'lucide-react';

const navigation = [
  { name: 'Dashboard', href: '/dashboard', icon: LayoutDashboard },
  { name: 'My Businesses', href: '/businesses', icon: Building2 },
  { name: 'Call Logs', href: '/call-logs', icon: Phone },
  { name: 'Appointments', href: '/appointments', icon: Calendar },
  { name: 'Analytics', href: '/analytics', icon: BarChart3 },
];

const toolsNavigation = [
  { name: 'Voice Preview', href: '/preview', icon: Play },
  { name: 'AI Training', href: '/ai-training', icon: Headphones },
  { name: 'Business Hours', href: '/business-hours', icon: Clock },
];

const quickActions = [
  { name: 'Add Business', href: '/businesses/new', icon: Plus },
];

const secondaryNavigation = [
  { name: 'Settings', href: '/settings', icon: Settings },
  { name: 'Help & Support', href: '/help', icon: HelpCircle },
];

const Sidebar = () => {
  const location = useLocation();

  const isActive = (href) => {
    return location.pathname === href || location.pathname.startsWith(href + '/');
  };

  const NavItem = ({ item }) => (
    <Link
      to={item.href}
      className={`group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors ${
        isActive(item.href)
          ? 'bg-primary-100 text-primary-700 border-r-2 border-primary-600'
          : 'text-secondary-600 hover:bg-secondary-50 hover:text-secondary-900'
      }`}
    >
      <item.icon
        className={`mr-3 flex-shrink-0 h-5 w-5 ${
          isActive(item.href) ? 'text-primary-600' : 'text-secondary-400 group-hover:text-secondary-500'
        }`}
      />
      {item.name}
    </Link>
  );

  return (
    <div className="flex flex-col w-64 bg-white border-r border-secondary-200 h-full">
      {/* Quick Actions */}
      <div className="px-4 pt-6 pb-4">
        <div className="space-y-2">
          {quickActions.map((item) => (
            <Link
              key={item.name}
              to={item.href}
              className="group flex items-center px-3 py-2 text-sm font-medium rounded-md bg-primary-600 text-white hover:bg-primary-700 transition-colors"
            >
              <item.icon className="mr-3 flex-shrink-0 h-4 w-4" />
              {item.name}
            </Link>
          ))}
        </div>
      </div>

      {/* Main Navigation */}
      <nav className="flex-1 px-4 space-y-1">
        {/* Core Features */}
        <div className="space-y-1">
          <h3 className="px-2 text-xs font-semibold text-secondary-500 uppercase tracking-wider mb-2">
            Overview
          </h3>
          {navigation.map((item) => (
            <NavItem key={item.name} item={item} />
          ))}
        </div>
        
        {/* Tools & Configuration */}
        <div className="pt-6">
          <h3 className="px-2 text-xs font-semibold text-secondary-500 uppercase tracking-wider mb-2">
            Tools & Setup
          </h3>
          <div className="space-y-1">
            {toolsNavigation.map((item) => (
              <NavItem key={item.name} item={item} />
            ))}
          </div>
        </div>
        
        {/* Divider */}
        <div className="border-t border-secondary-200 my-6"></div>
        
        {/* Account & Support */}
        <div className="space-y-1">
          <h3 className="px-2 text-xs font-semibold text-secondary-500 uppercase tracking-wider mb-2">
            Account
          </h3>
          {secondaryNavigation.map((item) => (
            <NavItem key={item.name} item={item} />
          ))}
        </div>
      </nav>

      {/* Footer */}
      <div className="px-4 py-4 border-t border-secondary-200">
        <div className="text-xs text-secondary-500 text-center">
          <p className="font-medium">Connecto AI</p>
          <p>by SME Analytica</p>
          <p className="mt-1 text-secondary-400">v1.0.0</p>
        </div>
      </div>
    </div>
  );
};

export default Sidebar;
