import React, { useState, useEffect } from 'react';
import { Calendar, Clock, Users, Plus, ExternalLink, CheckCircle, AlertCircle } from 'lucide-react';
import Button from '../common/Button';
import Input from '../common/Input';
import Select from '../common/Select';
import { Card, CardHeader, CardTitle, CardContent } from '../common/Card';
import LoadingSpinner from '../common/LoadingSpinner';
import toast from 'react-hot-toast';

const CalendarIntegration = ({ businessId, businessType, onUpdate }) => {
  const [integrations, setIntegrations] = useState({
    google_calendar: { connected: false, email: '' },
    outlook: { connected: false, email: '' },
    calendly: { connected: false, username: '' }
  });
  const [appointments, setAppointments] = useState([]);
  const [serviceTypes, setServiceTypes] = useState([]);
  const [newService, setNewService] = useState({
    name: '',
    duration: 30,
    price: '',
    description: ''
  });
  const [loading, setLoading] = useState(false);
  const [showAddService, setShowAddService] = useState(false);

  useEffect(() => {
    fetchCalendarData();
    loadDefaultServices();
  }, [businessId, businessType]);

  const loadDefaultServices = () => {
    const defaultServices = getDefaultServicesForBusinessType(businessType);
    setServiceTypes(defaultServices);
  };

  const getDefaultServicesForBusinessType = (type) => {
    const serviceTemplates = {
      law_firm: [
        { name: 'Initial Consultation', duration: 60, price: '$200', description: 'First meeting with attorney' },
        { name: 'Document Review', duration: 30, price: '$150', description: 'Review legal documents' },
        { name: 'Court Hearing Prep', duration: 90, price: '$300', description: 'Preparation for court hearing' }
      ],
      medical_office: [
        { name: 'Check-up', duration: 30, price: '$150', description: 'Routine medical examination' },
        { name: 'Consultation', duration: 45, price: '$200', description: 'Medical consultation' },
        { name: 'Follow-up', duration: 20, price: '$100', description: 'Follow-up appointment' }
      ],
      dental_office: [
        { name: 'Cleaning', duration: 60, price: '$120', description: 'Dental cleaning and checkup' },
        { name: 'Filling', duration: 45, price: '$180', description: 'Dental filling procedure' },
        { name: 'Consultation', duration: 30, price: '$80', description: 'Dental consultation' }
      ],
      auto_service: [
        { name: 'Oil Change', duration: 30, price: '$50', description: 'Standard oil change service' },
        { name: 'Brake Inspection', duration: 45, price: '$75', description: 'Brake system inspection' },
        { name: 'Diagnostic', duration: 60, price: '$100', description: 'Vehicle diagnostic check' }
      ],
      spa: [
        { name: 'Swedish Massage', duration: 60, price: '$90', description: '60-minute relaxation massage' },
        { name: 'Facial Treatment', duration: 45, price: '$85', description: 'Deep cleansing facial' },
        { name: 'Manicure/Pedicure', duration: 75, price: '$65', description: 'Complete nail care' }
      ],
      fitness_center: [
        { name: 'Personal Training', duration: 60, price: '$80', description: 'One-on-one training session' },
        { name: 'Group Class', duration: 45, price: '$25', description: 'Group fitness class' },
        { name: 'Consultation', duration: 30, price: '$50', description: 'Fitness consultation' }
      ],
      restaurant: [
        { name: 'Dinner Reservation', duration: 120, price: 'Free', description: 'Table reservation for dinner' },
        { name: 'Private Event', duration: 240, price: 'Quote', description: 'Private dining event' },
        { name: 'Catering Consultation', duration: 45, price: 'Free', description: 'Catering planning meeting' }
      ]
    };

    return serviceTemplates[type] || [
      { name: 'Consultation', duration: 45, price: '$100', description: 'General consultation' },
      { name: 'Service Appointment', duration: 60, price: '$150', description: 'Standard service appointment' }
    ];
  };

  const fetchCalendarData = async () => {
    try {
      setLoading(true);
      
      console.log('📅 Fetching REAL calendar data for business:', businessId);
      
      // Real calendar API integration should be implemented here
      // This would call your backend which fetches data from connected calendar providers
      
      try {
        // TODO: Implement real calendar API calls
        // const response = await calendarAPI.getAppointments(businessId);
        // const realAppointments = response.data.appointments || [];
        // setAppointments(realAppointments);
        
        console.log('📅 Real calendar API integration needed - no mock data');
        
        // For now, set empty array until real integration is implemented
        setAppointments([]);
        
      } catch (apiError) {
        console.error('❌ Failed to fetch calendar data:', apiError);
        setAppointments([]);
      }
      
    } catch (error) {
      console.error('Error fetching calendar data:', error);
      setAppointments([]);
    } finally {
      setLoading(false);
    }
  };

  const connectCalendar = async (provider) => {
    try {
      setLoading(true);
      
      console.log(`🔗 Initiating REAL ${provider} OAuth integration...`);
      
      // Real OAuth integration should redirect to actual provider OAuth URLs
      // This is where you would redirect to Google Calendar, Outlook, or Calendly OAuth
      
      try {
        // TODO: Implement real OAuth flow
        // This should redirect to the actual OAuth provider:
        // 
        // For Google Calendar:
        // window.location.href = `https://accounts.google.com/oauth/authorize?client_id=${GOOGLE_CLIENT_ID}&redirect_uri=${REDIRECT_URI}&scope=${GOOGLE_SCOPES}&response_type=code&state=${businessId}_google`;
        // 
        // For Outlook:
        // window.location.href = `https://login.microsoftonline.com/common/oauth2/authorize?client_id=${OUTLOOK_CLIENT_ID}&redirect_uri=${REDIRECT_URI}&scope=${OUTLOOK_SCOPES}&response_type=code&state=${businessId}_outlook`;
        // 
        // For Calendly:
        // window.location.href = `https://auth.calendly.com/oauth/authorize?client_id=${CALENDLY_CLIENT_ID}&redirect_uri=${REDIRECT_URI}&scope=${CALENDLY_SCOPES}&response_type=code&state=${businessId}_calendly`;
        
        console.log(`📅 Real ${provider} OAuth integration needed`);
        toast.error(`Real ${provider} OAuth integration not yet implemented. Please configure OAuth credentials.`);
        
      } catch (apiError) {
        console.error(`❌ Failed to initiate ${provider} OAuth:`, apiError);
        toast.error(`Failed to connect ${provider}`);
      }
      
    } catch (error) {
      console.error(`Error connecting ${provider}:`, error);
      toast.error(`Failed to connect ${provider}`);
    } finally {
      setLoading(false);
    }
  };

  const disconnectCalendar = async (provider) => {
    setIntegrations(prev => ({
      ...prev,
      [provider]: { connected: false, email: '' }
    }));
    toast.success(`${provider.replace('_', ' ')} disconnected`);
  };

  const addService = async () => {
    if (!newService.name || !newService.duration) {
      toast.error('Please fill in required fields');
      return;
    }

    const service = {
      id: Date.now(),
      ...newService
    };

    setServiceTypes(prev => [...prev, service]);
    setNewService({ name: '', duration: 30, price: '', description: '' });
    setShowAddService(false);
    toast.success('Service added successfully');
  };

  const removeService = (serviceId) => {
    setServiceTypes(prev => prev.filter(s => s.id !== serviceId));
    toast.success('Service removed');
  };

  if (loading && appointments.length === 0) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center p-8">
          <LoadingSpinner size="lg" text="Loading calendar integration..." />
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Calendar Integrations */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="w-5 h-5" />
            Calendar Integrations
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Google Calendar */}
          <div className="flex items-center justify-between p-4 border border-secondary-200 rounded-lg">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
                <Calendar className="w-4 h-4 text-white" />
              </div>
              <div>
                <h4 className="font-medium text-secondary-900">Google Calendar</h4>
                <p className="text-sm text-secondary-600">
                  {integrations.google_calendar.connected ? 
                    `Connected: ${integrations.google_calendar.email}` : 
                    'Sync appointments with Google Calendar'}
                </p>
              </div>
            </div>
            {integrations.google_calendar.connected ? (
              <div className="flex items-center gap-2">
                <CheckCircle className="w-5 h-5 text-success-600" />
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => disconnectCalendar('google_calendar')}
                >
                  Disconnect
                </Button>
              </div>
            ) : (
              <Button
                onClick={() => connectCalendar('google_calendar')}
                loading={loading}
                size="sm"
              >
                Connect
              </Button>
            )}
          </div>

          {/* Outlook Calendar */}
          <div className="flex items-center justify-between p-4 border border-secondary-200 rounded-lg">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                <Calendar className="w-4 h-4 text-white" />
              </div>
              <div>
                <h4 className="font-medium text-secondary-900">Outlook Calendar</h4>
                <p className="text-sm text-secondary-600">
                  {integrations.outlook.connected ? 
                    `Connected: ${integrations.outlook.email}` : 
                    'Sync appointments with Outlook Calendar'}
                </p>
              </div>
            </div>
            {integrations.outlook.connected ? (
              <div className="flex items-center gap-2">
                <CheckCircle className="w-5 h-5 text-success-600" />
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => disconnectCalendar('outlook')}
                >
                  Disconnect
                </Button>
              </div>
            ) : (
              <Button
                onClick={() => connectCalendar('outlook')}
                loading={loading}
                size="sm"
              >
                Connect
              </Button>
            )}
          </div>

          {/* Calendly */}
          <div className="flex items-center justify-between p-4 border border-secondary-200 rounded-lg">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-orange-500 rounded-lg flex items-center justify-center">
                <ExternalLink className="w-4 h-4 text-white" />
              </div>
              <div>
                <h4 className="font-medium text-secondary-900">Calendly</h4>
                <p className="text-sm text-secondary-600">
                  {integrations.calendly.connected ? 
                    `Connected: ${integrations.calendly.username}` : 
                    'Integrate with Calendly for online booking'}
                </p>
              </div>
            </div>
            {integrations.calendly.connected ? (
              <div className="flex items-center gap-2">
                <CheckCircle className="w-5 h-5 text-success-600" />
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => disconnectCalendar('calendly')}
                >
                  Disconnect
                </Button>
              </div>
            ) : (
              <Button
                onClick={() => connectCalendar('calendly')}
                loading={loading}
                size="sm"
              >
                Connect
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Service Types Configuration */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Users className="w-5 h-5" />
              Service Types & Appointments
            </CardTitle>
            <Button
              size="sm"
              icon={Plus}
              onClick={() => setShowAddService(true)}
            >
              Add Service
            </Button>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          {showAddService && (
            <div className="p-4 border border-primary-200 bg-primary-50 rounded-lg space-y-4">
              <h4 className="font-medium text-secondary-900">Add New Service</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Input
                  label="Service Name"
                  value={newService.name}
                  onChange={(e) => setNewService(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="e.g., Consultation"
                />
                <Input
                  label="Duration (minutes)"
                  type="number"
                  value={newService.duration}
                  onChange={(e) => setNewService(prev => ({ ...prev, duration: parseInt(e.target.value) }))}
                  placeholder="30"
                />
                <Input
                  label="Price"
                  value={newService.price}
                  onChange={(e) => setNewService(prev => ({ ...prev, price: e.target.value }))}
                  placeholder="$100"
                />
                <Input
                  label="Description"
                  value={newService.description}
                  onChange={(e) => setNewService(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="Brief description"
                />
              </div>
              <div className="flex items-center gap-2">
                <Button onClick={addService} size="sm">
                  Add Service
                </Button>
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => setShowAddService(false)}
                >
                  Cancel
                </Button>
              </div>
            </div>
          )}

          <div className="space-y-3">
            {serviceTypes.map((service, index) => (
              <div key={service.id || index} className="flex items-center justify-between p-3 border border-secondary-200 rounded-lg">
                <div className="flex items-center gap-3">
                  <Clock className="w-4 h-4 text-secondary-500" />
                  <div>
                    <h4 className="font-medium text-secondary-900">{service.name}</h4>
                    <p className="text-sm text-secondary-600">
                      {service.duration} minutes • {service.price}
                      {service.description && ` • ${service.description}`}
                    </p>
                  </div>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => removeService(service.id || index)}
                  className="text-error-600 hover:text-error-700"
                >
                  Remove
                </Button>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Recent Appointments */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Appointments</CardTitle>
        </CardHeader>
        <CardContent>
          {appointments.length === 0 ? (
            <div className="text-center py-8">
              <Calendar className="w-12 h-12 text-secondary-400 mx-auto mb-4" />
              <p className="text-secondary-600">No appointments scheduled yet</p>
              <p className="text-sm text-secondary-500 mt-1">
                Connect your calendar to start receiving AI-booked appointments
              </p>
            </div>
          ) : (
            <div className="space-y-3">
              {appointments.map((appointment) => (
                <div key={appointment.id} className="flex items-center justify-between p-3 border border-secondary-200 rounded-lg">
                  <div>
                    <h4 className="font-medium text-secondary-900">{appointment.client_name}</h4>
                    <p className="text-sm text-secondary-600">
                      {appointment.service} • {new Date(appointment.date).toLocaleDateString()} at {new Date(appointment.date).toLocaleTimeString()}
                    </p>
                  </div>
                  <span className={`px-2 py-1 text-xs rounded-full ${
                    appointment.status === 'confirmed' ? 'bg-success-100 text-success-800' :
                    appointment.status === 'pending' ? 'bg-warning-100 text-warning-800' :
                    'bg-secondary-100 text-secondary-800'
                  }`}>
                    {appointment.status}
                  </span>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default CalendarIntegration;