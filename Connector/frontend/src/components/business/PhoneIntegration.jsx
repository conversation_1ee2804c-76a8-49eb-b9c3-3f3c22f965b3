import React, { useState, useEffect } from 'react';
import { Phone, AlertCircle, CheckCircle, Settings, Copy } from 'lucide-react';
import Button from '../common/Button';
import Input from '../common/Input';
import { Card, CardHeader, CardTitle, CardContent } from '../common/Card';
import LoadingSpinner from '../common/LoadingSpinner';
import toast from 'react-hot-toast';

const PhoneIntegration = ({ businessId, onUpdate }) => {
  const [phoneConfig, setPhoneConfig] = useState({
    business_phone: '',
    sip_enabled: false,
    forwarding_number: '',
    voicemail_enabled: true,
    call_recording: false,
    office_hours_only: true,
    auto_attendant: true
  });
  const [loading, setLoading] = useState(false);
  const [sipCredentials, setSipCredentials] = useState(null);
  const [testCallStatus, setTestCallStatus] = useState(null);

  useEffect(() => {
    fetchPhoneConfig();
  }, [businessId]);

  const fetchPhoneConfig = async () => {
    try {
      setLoading(true);
      // In a real implementation, fetch from API
      // const response = await api.get(`/businesses/${businessId}/phone-config`);
      // setPhoneConfig(response.data.data);
    } catch (error) {
      console.error('Error fetching phone config:', error);
      toast.error('Failed to load phone configuration');
    } finally {
      setLoading(false);
    }
  };

  const handleConfigUpdate = async (updates) => {
    try {
      setLoading(true);
      setPhoneConfig(prev => ({ ...prev, ...updates }));
      
      // In a real implementation, save to API
      // await api.put(`/businesses/${businessId}/phone-config`, updates);
      
      toast.success('Phone configuration updated');
      onUpdate && onUpdate(updates);
    } catch (error) {
      console.error('Error updating phone config:', error);
      toast.error('Failed to update phone configuration');
    } finally {
      setLoading(false);
    }
  };

  const enableSipIntegration = async () => {
    try {
      setLoading(true);
      
      // Simulate SIP setup process
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Mock SIP credentials
      const credentials = {
        username: `biz_${businessId}_sip`,
        password: `sip_${Math.random().toString(36).substring(7)}`,
        domain: 'sip.connecto.ai',
        port: 5060
      };
      
      setSipCredentials(credentials);
      setPhoneConfig(prev => ({ ...prev, sip_enabled: true }));
      
      toast.success('SIP integration enabled successfully!');
    } catch (error) {
      console.error('Error enabling SIP:', error);
      toast.error('Failed to enable SIP integration');
    } finally {
      setLoading(false);
    }
  };

  const testPhoneConnection = async () => {
    try {
      setTestCallStatus('testing');
      
      // Simulate test call
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      setTestCallStatus('success');
      toast.success('Test call completed successfully!');
      
      setTimeout(() => setTestCallStatus(null), 5000);
    } catch (error) {
      setTestCallStatus('failed');
      toast.error('Test call failed');
      setTimeout(() => setTestCallStatus(null), 5000);
    }
  };

  const copyToClipboard = (text) => {
    navigator.clipboard.writeText(text);
    toast.success('Copied to clipboard');
  };

  if (loading && !phoneConfig.business_phone) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center p-8">
          <LoadingSpinner size="lg" text="Loading phone configuration..." />
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Phone Number Configuration */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Phone className="w-5 h-5" />
            Phone Number Setup
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <Input
            label="Business Phone Number"
            type="tel"
            value={phoneConfig.business_phone}
            onChange={(e) => setPhoneConfig(prev => ({ ...prev, business_phone: e.target.value }))}
            placeholder="+****************"
            help="Enter your main business phone number that customers call"
          />
          
          <div className="flex items-center gap-4">
            <Button
              onClick={() => handleConfigUpdate({ business_phone: phoneConfig.business_phone })}
              disabled={!phoneConfig.business_phone || loading}
            >
              Save Phone Number
            </Button>
            
            {phoneConfig.business_phone && !phoneConfig.sip_enabled && (
              <Button
                variant="outline"
                onClick={enableSipIntegration}
                loading={loading}
                icon={Settings}
              >
                Enable AI Integration
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      {/* SIP Integration Status */}
      {phoneConfig.sip_enabled && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CheckCircle className="w-5 h-5 text-success-600" />
              AI Voice Integration Active
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="p-4 bg-success-50 border border-success-200 rounded-lg">
              <p className="text-success-800 font-medium">
                Your phone number is now connected to Connecto AI!
              </p>
              <p className="text-success-700 text-sm mt-1">
                Incoming calls will be handled by your AI voice receptionist.
              </p>
            </div>

            {/* SIP Credentials */}
            {sipCredentials && (
              <div className="space-y-3">
                <h4 className="font-medium text-secondary-900">SIP Configuration Details:</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                  <div className="flex items-center justify-between p-2 bg-secondary-50 rounded">
                    <span className="text-secondary-600">Username:</span>
                    <div className="flex items-center gap-2">
                      <code className="text-secondary-900">{sipCredentials.username}</code>
                      <Button
                        variant="ghost"
                        size="sm"
                        icon={Copy}
                        onClick={() => copyToClipboard(sipCredentials.username)}
                      />
                    </div>
                  </div>
                  <div className="flex items-center justify-between p-2 bg-secondary-50 rounded">
                    <span className="text-secondary-600">Domain:</span>
                    <div className="flex items-center gap-2">
                      <code className="text-secondary-900">{sipCredentials.domain}</code>
                      <Button
                        variant="ghost"
                        size="sm"
                        icon={Copy}
                        onClick={() => copyToClipboard(sipCredentials.domain)}
                      />
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Test Call */}
            <div className="flex items-center gap-4">
              <Button
                variant="outline"
                onClick={testPhoneConnection}
                loading={testCallStatus === 'testing'}
                icon={Phone}
              >
                {testCallStatus === 'testing' ? 'Testing...' : 'Test AI Response'}
              </Button>
              
              {testCallStatus === 'success' && (
                <div className="flex items-center gap-2 text-success-600">
                  <CheckCircle className="w-4 h-4" />
                  <span className="text-sm">Test successful</span>
                </div>
              )}
              
              {testCallStatus === 'failed' && (
                <div className="flex items-center gap-2 text-error-600">
                  <AlertCircle className="w-4 h-4" />
                  <span className="text-sm">Test failed</span>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Call Routing Options */}
      <Card>
        <CardHeader>
          <CardTitle>Call Routing Options</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-3">
            <label className="flex items-center gap-3">
              <input
                type="checkbox"
                checked={phoneConfig.office_hours_only}
                onChange={(e) => handleConfigUpdate({ office_hours_only: e.target.checked })}
                className="rounded border-secondary-300 text-primary-600 focus:ring-primary-500"
              />
              <div>
                <span className="font-medium text-secondary-900">Office Hours Only</span>
                <p className="text-sm text-secondary-600">Only handle calls during business hours</p>
              </div>
            </label>

            <label className="flex items-center gap-3">
              <input
                type="checkbox"
                checked={phoneConfig.voicemail_enabled}
                onChange={(e) => handleConfigUpdate({ voicemail_enabled: e.target.checked })}
                className="rounded border-secondary-300 text-primary-600 focus:ring-primary-500"
              />
              <div>
                <span className="font-medium text-secondary-900">AI Voicemail</span>
                <p className="text-sm text-secondary-600">Take messages when unavailable</p>
              </div>
            </label>

            <label className="flex items-center gap-3">
              <input
                type="checkbox"
                checked={phoneConfig.call_recording}
                onChange={(e) => handleConfigUpdate({ call_recording: e.target.checked })}
                className="rounded border-secondary-300 text-primary-600 focus:ring-primary-500"
              />
              <div>
                <span className="font-medium text-secondary-900">Call Recording</span>
                <p className="text-sm text-secondary-600">Record calls for quality and training</p>
              </div>
            </label>
          </div>

          <Input
            label="Forwarding Number (Optional)"
            type="tel"
            value={phoneConfig.forwarding_number}
            onChange={(e) => setPhoneConfig(prev => ({ ...prev, forwarding_number: e.target.value }))}
            placeholder="+****************"
            help="Forward calls to this number when AI can't handle them"
          />

          <Button
            onClick={() => handleConfigUpdate({
              forwarding_number: phoneConfig.forwarding_number,
              office_hours_only: phoneConfig.office_hours_only,
              voicemail_enabled: phoneConfig.voicemail_enabled,
              call_recording: phoneConfig.call_recording
            })}
            disabled={loading}
          >
            Save Routing Settings
          </Button>
        </CardContent>
      </Card>
    </div>
  );
};

export default PhoneIntegration;