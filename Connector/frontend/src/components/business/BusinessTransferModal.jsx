import React, { useState } from 'react';
import { X, Building2, ArrowRight, CheckCircle, AlertCircle, Info } from 'lucide-react';
import Button from '../common/Button';
import { Card, CardHeader, CardTitle, CardContent } from '../common/Card';
import { businessAPI } from '../../utils/api';
import { snakeToTitle } from '../../utils/helpers';
import toast from 'react-hot-toast';

const BusinessTransferModal = ({ isOpen, onClose, businesses, onTransferComplete }) => {
  const [selectedBusinesses, setSelectedBusinesses] = useState([]);
  const [transferring, setTransferring] = useState(false);
  const [step, setStep] = useState('select'); // 'select', 'confirm', 'complete'

  if (!isOpen) return null;

  const handleBusinessToggle = (businessId) => {
    setSelectedBusinesses(prev => 
      prev.includes(businessId) 
        ? prev.filter(id => id !== businessId)
        : [...prev, businessId]
    );
  };

  const handleTransfer = async () => {
    if (selectedBusinesses.length === 0) {
      toast.error('Please select at least one business to transfer');
      return;
    }

    setTransferring(true);
    try {
      const results = [];
      
      for (const businessId of selectedBusinesses) {
        const result = await businessAPI.transfer(businessId);
        results.push(result.data);
      }

      setStep('complete');
      toast.success(`Successfully transferred ${selectedBusinesses.length} business(es) to Connecto!`);
      
      // Call the callback to refresh the dashboard
      if (onTransferComplete) {
        onTransferComplete(results);
      }
    } catch (error) {
      console.error('Transfer error:', error);
      toast.error('Failed to transfer businesses. Please try again.');
    } finally {
      setTransferring(false);
    }
  };

  const getConfidenceColor = (confidence) => {
    switch (confidence) {
      case 'high': return 'text-success-600 bg-success-100';
      case 'medium': return 'text-warning-600 bg-warning-100';
      case 'low': return 'text-error-600 bg-error-100';
      default: return 'text-secondary-600 bg-secondary-100';
    }
  };

  const getConfidenceIcon = (confidence) => {
    switch (confidence) {
      case 'high': return CheckCircle;
      case 'medium': return AlertCircle;
      case 'low': return AlertCircle;
      default: return Info;
    }
  };

  const renderSelectStep = () => (
    <div className="space-y-6">
      <div className="text-center">
        <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <Building2 className="w-8 h-8 text-primary-600" />
        </div>
        <h3 className="text-xl font-semibold text-secondary-900 mb-2">
          Transfer Existing Businesses
        </h3>
        <p className="text-secondary-600">
          We found {businesses.length} business(es) from your other SME Analytica applications that can be added to Connecto.
        </p>
      </div>

      <div className="space-y-4 max-h-96 overflow-y-auto">
        {businesses.map((business) => {
          const ConfidenceIcon = getConfidenceIcon(business.transfer_confidence);
          
          return (
            <Card 
              key={business.id} 
              className={`cursor-pointer transition-all ${
                selectedBusinesses.includes(business.id) 
                  ? 'ring-2 ring-primary-500 bg-primary-50' 
                  : 'hover:shadow-medium'
              }`}
              onClick={() => handleBusinessToggle(business.id)}
            >
              <CardContent className="p-4">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      <input
                        type="checkbox"
                        checked={selectedBusinesses.includes(business.id)}
                        onChange={() => handleBusinessToggle(business.id)}
                        className="w-4 h-4 text-primary-600 border-secondary-300 rounded focus:ring-primary-500"
                      />
                      <h4 className="font-semibold text-secondary-900">{business.name}</h4>
                      <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getConfidenceColor(business.transfer_confidence)}`}>
                        <ConfidenceIcon className="w-3 h-3 mr-1" />
                        {business.transfer_confidence} confidence
                      </div>
                    </div>
                    
                    <div className="text-sm text-secondary-600 space-y-1">
                      <p><strong>Email:</strong> {business.contact_email}</p>
                      <p><strong>Phone:</strong> {business.contact_phone}</p>
                      <p><strong>Address:</strong> {business.address}</p>
                      <p><strong>Currently used in:</strong> {business.current_applications?.map(app => snakeToTitle(app)).join(', ')}</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      <div className="bg-secondary-50 rounded-lg p-4">
        <div className="flex items-start space-x-3">
          <Info className="w-5 h-5 text-primary-600 mt-0.5" />
          <div className="text-sm text-secondary-700">
            <p className="font-medium mb-1">What happens when you transfer?</p>
            <ul className="space-y-1 text-xs">
              <li>• Your business will be registered with Connecto AI Voice Receptionist</li>
              <li>• All existing data (name, contact info, hours) will be preserved</li>
              <li>• A default AI agent configuration will be created</li>
              <li>• Your business will remain active in other applications</li>
            </ul>
          </div>
        </div>
      </div>

      <div className="flex justify-between">
        <Button variant="ghost" onClick={onClose}>
          Cancel
        </Button>
        <Button 
          onClick={() => setStep('confirm')}
          disabled={selectedBusinesses.length === 0}
        >
          Continue ({selectedBusinesses.length} selected)
        </Button>
      </div>
    </div>
  );

  const renderConfirmStep = () => (
    <div className="space-y-6">
      <div className="text-center">
        <div className="w-16 h-16 bg-warning-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <ArrowRight className="w-8 h-8 text-warning-600" />
        </div>
        <h3 className="text-xl font-semibold text-secondary-900 mb-2">
          Confirm Transfer
        </h3>
        <p className="text-secondary-600">
          You're about to transfer {selectedBusinesses.length} business(es) to Connecto.
        </p>
      </div>

      <div className="bg-warning-50 border border-warning-200 rounded-lg p-4">
        <h4 className="font-medium text-warning-800 mb-2">Selected Businesses:</h4>
        <ul className="space-y-1">
          {selectedBusinesses.map(businessId => {
            const business = businesses.find(b => b.id === businessId);
            return (
              <li key={businessId} className="text-sm text-warning-700">
                • {business?.name}
              </li>
            );
          })}
        </ul>
      </div>

      <div className="flex justify-between">
        <Button variant="ghost" onClick={() => setStep('select')}>
          Back
        </Button>
        <Button 
          onClick={handleTransfer}
          loading={transferring}
          disabled={transferring}
        >
          Transfer Businesses
        </Button>
      </div>
    </div>
  );

  const renderCompleteStep = () => (
    <div className="space-y-6 text-center">
      <div className="w-16 h-16 bg-success-100 rounded-full flex items-center justify-center mx-auto">
        <CheckCircle className="w-8 h-8 text-success-600" />
      </div>
      
      <div>
        <h3 className="text-xl font-semibold text-secondary-900 mb-2">
          Transfer Complete!
        </h3>
        <p className="text-secondary-600">
          Your businesses have been successfully added to Connecto. You can now configure their AI voice receptionists.
        </p>
      </div>

      <Button onClick={onClose} className="w-full">
        Continue to Dashboard
      </Button>
    </div>
  );

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-large max-w-2xl w-full max-h-[90vh] overflow-hidden">
        <div className="flex items-center justify-between p-6 border-b border-secondary-200">
          <h2 className="text-lg font-semibold text-secondary-900">
            Business Transfer
          </h2>
          <button
            onClick={onClose}
            className="text-secondary-400 hover:text-secondary-600"
          >
            <X className="w-5 h-5" />
          </button>
        </div>
        
        <div className="p-6 overflow-y-auto">
          {step === 'select' && renderSelectStep()}
          {step === 'confirm' && renderConfirmStep()}
          {step === 'complete' && renderCompleteStep()}
        </div>
      </div>
    </div>
  );
};

export default BusinessTransferModal;
