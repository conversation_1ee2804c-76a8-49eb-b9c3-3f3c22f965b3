import React, { useState, useEffect } from 'react';
import { Clock, Copy, RotateCcw } from 'lucide-react';
import Button from '../common/Button';
import Select from '../common/Select';
import { <PERSON>, CardHeader, CardTitle, CardContent } from '../common/Card';
import toast from 'react-hot-toast';

const BusinessHours = ({ businessId, onUpdate }) => {
  const [hours, setHours] = useState({
    monday: { open: '09:00', close: '17:00', closed: false },
    tuesday: { open: '09:00', close: '17:00', closed: false },
    wednesday: { open: '09:00', close: '17:00', closed: false },
    thursday: { open: '09:00', close: '17:00', closed: false },
    friday: { open: '09:00', close: '17:00', closed: false },
    saturday: { open: '10:00', close: '16:00', closed: false },
    sunday: { open: '12:00', close: '16:00', closed: true }
  });
  const [holidays, setHolidays] = useState([]);
  const [specialHours, setSpecialHours] = useState([]);
  const [timezone, setTimezone] = useState('America/New_York');

  const daysOfWeek = [
    { key: 'monday', label: 'Monday' },
    { key: 'tuesday', label: 'Tuesday' },
    { key: 'wednesday', label: 'Wednesday' },
    { key: 'thursday', label: 'Thursday' },
    { key: 'friday', label: 'Friday' },
    { key: 'saturday', label: 'Saturday' },
    { key: 'sunday', label: 'Sunday' }
  ];

  const timezones = [
    { value: 'America/New_York', label: 'Eastern Time (ET)' },
    { value: 'America/Chicago', label: 'Central Time (CT)' },
    { value: 'America/Denver', label: 'Mountain Time (MT)' },
    { value: 'America/Los_Angeles', label: 'Pacific Time (PT)' },
    { value: 'Europe/London', label: 'Greenwich Mean Time (GMT)' },
    { value: 'Europe/Paris', label: 'Central European Time (CET)' },
    { value: 'Asia/Tokyo', label: 'Japan Standard Time (JST)' },
    { value: 'Australia/Sydney', label: 'Australian Eastern Time (AET)' }
  ];

  const timeSlots = [];
  for (let hour = 0; hour < 24; hour++) {
    for (let minute = 0; minute < 60; minute += 30) {
      const time = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
      const displayTime = new Date(`2000-01-01T${time}`).toLocaleTimeString([], { 
        hour: 'numeric', 
        minute: '2-digit',
        hour12: true 
      });
      timeSlots.push({ value: time, label: displayTime });
    }
  }

  useEffect(() => {
    fetchBusinessHours();
  }, [businessId]);

  const fetchBusinessHours = async () => {
    try {
      // In a real implementation, fetch from API
      // const response = await api.get(`/businesses/${businessId}/hours`);
      // setHours(response.data.data.hours);
      // setTimezone(response.data.data.timezone);
    } catch (error) {
      console.error('Error fetching business hours:', error);
    }
  };

  const updateDayHours = (day, field, value) => {
    setHours(prev => ({
      ...prev,
      [day]: {
        ...prev[day],
        [field]: value
      }
    }));
  };

  const toggleDayClosed = (day) => {
    setHours(prev => ({
      ...prev,
      [day]: {
        ...prev[day],
        closed: !prev[day].closed
      }
    }));
  };

  const copyFromPreviousDay = (day) => {
    const dayIndex = daysOfWeek.findIndex(d => d.key === day);
    if (dayIndex > 0) {
      const previousDay = daysOfWeek[dayIndex - 1].key;
      setHours(prev => ({
        ...prev,
        [day]: { ...prev[previousDay] }
      }));
      toast.success(`Copied hours from ${daysOfWeek[dayIndex - 1].label}`);
    }
  };

  const applyToAllDays = (day) => {
    const dayHours = hours[day];
    const updatedHours = {};
    daysOfWeek.forEach(d => {
      updatedHours[d.key] = { ...dayHours };
    });
    setHours(updatedHours);
    toast.success('Applied to all days');
  };

  const resetToDefaults = () => {
    setHours({
      monday: { open: '09:00', close: '17:00', closed: false },
      tuesday: { open: '09:00', close: '17:00', closed: false },
      wednesday: { open: '09:00', close: '17:00', closed: false },
      thursday: { open: '09:00', close: '17:00', closed: false },
      friday: { open: '09:00', close: '17:00', closed: false },
      saturday: { open: '10:00', close: '16:00', closed: false },
      sunday: { open: '12:00', close: '16:00', closed: true }
    });
    toast.success('Reset to default hours');
  };

  const saveBusinessHours = async () => {
    try {
      // In a real implementation, save to API
      // await api.put(`/businesses/${businessId}/hours`, { hours, timezone });
      
      toast.success('Business hours saved successfully');
      onUpdate && onUpdate({ hours, timezone });
    } catch (error) {
      console.error('Error saving business hours:', error);
      toast.error('Failed to save business hours');
    }
  };

  const formatHoursForDisplay = () => {
    return daysOfWeek.map(day => {
      const dayHours = hours[day.key];
      if (dayHours.closed) {
        return `${day.label}: Closed`;
      }
      return `${day.label}: ${timeSlots.find(t => t.value === dayHours.open)?.label} - ${timeSlots.find(t => t.value === dayHours.close)?.label}`;
    }).join('\n');
  };

  return (
    <div className="space-y-6">
      {/* Business Hours Configuration */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Clock className="w-5 h-5" />
              Business Hours
            </CardTitle>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                icon={RotateCcw}
                onClick={resetToDefaults}
              >
                Reset
              </Button>
              <Button
                variant="outline"
                size="sm"
                icon={Copy}
                onClick={() => {
                  navigator.clipboard.writeText(formatHoursForDisplay());
                  toast.success('Hours copied to clipboard');
                }}
              >
                Copy
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Timezone Selection */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-secondary-700 mb-2">
              Time Zone
            </label>
            <Select
              value={timezone}
              onChange={(e) => setTimezone(e.target.value)}
              options={timezones}
              placeholder="Select timezone"
            />
          </div>

          {/* Days of Week */}
          <div className="space-y-4">
            {daysOfWeek.map((day) => (
              <div key={day.key} className="flex items-center gap-4 p-4 border border-secondary-200 rounded-lg">
                <div className="w-24">
                  <span className="font-medium text-secondary-900">{day.label}</span>
                </div>
                
                <div className="flex items-center gap-2">
                  <label className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      checked={hours[day.key].closed}
                      onChange={() => toggleDayClosed(day.key)}
                      className="rounded border-secondary-300 text-primary-600 focus:ring-primary-500"
                    />
                    <span className="text-sm text-secondary-600">Closed</span>
                  </label>
                </div>

                {!hours[day.key].closed && (
                  <>
                    <div className="flex items-center gap-2">
                      <label className="text-sm text-secondary-600">Open:</label>
                      <select
                        value={hours[day.key].open}
                        onChange={(e) => updateDayHours(day.key, 'open', e.target.value)}
                        className="px-3 py-1 border border-secondary-300 rounded-md text-sm focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                      >
                        {timeSlots.map(slot => (
                          <option key={slot.value} value={slot.value}>
                            {slot.label}
                          </option>
                        ))}
                      </select>
                    </div>

                    <div className="flex items-center gap-2">
                      <label className="text-sm text-secondary-600">Close:</label>
                      <select
                        value={hours[day.key].close}
                        onChange={(e) => updateDayHours(day.key, 'close', e.target.value)}
                        className="px-3 py-1 border border-secondary-300 rounded-md text-sm focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                      >
                        {timeSlots.map(slot => (
                          <option key={slot.value} value={slot.value}>
                            {slot.label}
                          </option>
                        ))}
                      </select>
                    </div>
                  </>
                )}

                <div className="flex items-center gap-1 ml-auto">
                  {daysOfWeek.findIndex(d => d.key === day.key) > 0 && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => copyFromPreviousDay(day.key)}
                      className="text-xs"
                    >
                      Copy from above
                    </Button>
                  )}
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => applyToAllDays(day.key)}
                    className="text-xs"
                  >
                    Apply to all
                  </Button>
                </div>
              </div>
            ))}
          </div>

          <div className="flex items-center gap-4 pt-4">
            <Button onClick={saveBusinessHours}>
              Save Business Hours
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Quick Preview */}
      <Card>
        <CardHeader>
          <CardTitle>Hours Preview</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="bg-secondary-50 p-4 rounded-lg">
            <h4 className="font-medium text-secondary-900 mb-2">Customer-facing hours display:</h4>
            <div className="text-sm text-secondary-700 space-y-1">
              {daysOfWeek.map(day => {
                const dayHours = hours[day.key];
                return (
                  <div key={day.key} className="flex justify-between">
                    <span>{day.label}:</span>
                    <span>
                      {dayHours.closed ? (
                        'Closed'
                      ) : (
                        `${timeSlots.find(t => t.value === dayHours.open)?.label} - ${timeSlots.find(t => t.value === dayHours.close)?.label}`
                      )}
                    </span>
                  </div>
                );
              })}
            </div>
            <p className="text-xs text-secondary-500 mt-2">
              Timezone: {timezones.find(tz => tz.value === timezone)?.label}
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default BusinessHours;