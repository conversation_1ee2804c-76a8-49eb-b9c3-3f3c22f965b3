import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { Toaster } from 'react-hot-toast';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import Layout from './components/layout/Layout';
import LoadingSpinner from './components/common/LoadingSpinner';

// Pages
import Login from './pages/Login';
import Register from './pages/Register';
import Dashboard from './pages/Dashboard';
import Businesses from './pages/Businesses';
import NewBusiness from './pages/NewBusiness';
import BusinessDetail from './pages/BusinessDetail';
import CallLogs from './pages/CallLogs';
import VoicePreview from './pages/VoicePreview';
import Settings from './pages/Settings';
import Appointments from './pages/Appointments';
import Analytics from './pages/Analytics';
import AITraining from './pages/AITraining';
import Help from './pages/Help';

// Protected Route Component
const ProtectedRoute = ({ children }) => {
  const { isAuthenticated, loading } = useAuth();

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" text="Loading..." />
      </div>
    );
  }

  return isAuthenticated ? children : <Navigate to="/login" replace />;
};

// Public Route Component (redirect to dashboard if authenticated)
const PublicRoute = ({ children }) => {
  const { isAuthenticated, loading } = useAuth();

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" text="Loading..." />
      </div>
    );
  }

  return !isAuthenticated ? children : <Navigate to="/dashboard" replace />;
};

// OAuth Callback Component
const OAuthCallback = () => {
  React.useEffect(() => {
    const handleOAuthCallback = () => {
      const urlParams = new URLSearchParams(window.location.search);
      const code = urlParams.get('code');
      const state = urlParams.get('state');
      const error = urlParams.get('error');
      
      console.log('🔄 OAuth Callback Handler:', { 
        url: window.location.href, 
        code: code ? 'Present' : 'Missing',
        state,
        error 
      });
      
      // Construct the appointments URL with OAuth parameters
      const appointmentsUrl = new URL('/appointments', window.location.origin);
      
      // Preserve OAuth parameters
      if (code) appointmentsUrl.searchParams.set('code', code);
      if (state) appointmentsUrl.searchParams.set('state', state);
      if (error) appointmentsUrl.searchParams.set('error', error);
      
      // Add a flag to indicate this came from OAuth callback
      appointmentsUrl.searchParams.set('oauth_callback', 'true');
      
      console.log('🔄 Redirecting to:', appointmentsUrl.toString());
      
      // Redirect to appointments page with OAuth parameters
      window.location.href = appointmentsUrl.toString();
    };

    // Small delay to show loading message
    setTimeout(handleOAuthCallback, 1000);
  }, []);

  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto mb-4"></div>
        <p className="text-secondary-600">Completing calendar connection...</p>
        <p className="text-sm text-secondary-500 mt-2">Processing OAuth response...</p>
      </div>
    </div>
  );
};

function App() {
  return (
    <AuthProvider>
      <Router
        future={{
          v7_startTransition: true,
          v7_relativeSplatPath: true
        }}
      >
        <div className="App">
          <Routes>
            {/* Public Routes */}
            <Route
              path="/login"
              element={
                <PublicRoute>
                  <Login />
                </PublicRoute>
              }
            />
            <Route
              path="/register"
              element={
                <PublicRoute>
                  <Register />
                </PublicRoute>
              }
            />

            {/* Protected Routes */}
            <Route
              path="/"
              element={
                <ProtectedRoute>
                  <Layout />
                </ProtectedRoute>
              }
            >
              <Route index element={<Navigate to="/dashboard" replace />} />
              <Route path="dashboard" element={<Dashboard />} />
              <Route path="businesses" element={<Businesses />} />
              <Route path="businesses/new" element={<NewBusiness />} />
              <Route path="businesses/:id" element={<BusinessDetail />} />
              <Route path="call-logs" element={<CallLogs />} />
              <Route path="appointments" element={<Appointments />} />
              <Route path="analytics" element={<Analytics />} />
              <Route path="ai-training" element={<AITraining />} />
              <Route path="preview" element={<VoicePreview />} />
              <Route path="settings" element={<Settings />} />
              <Route path="help" element={<Help />} />
            </Route>

            {/* OAuth Callback Route */}
            <Route path="/oauth/callback" element={<OAuthCallback />} />

            {/* Catch all route */}
            <Route path="*" element={<Navigate to="/dashboard" replace />} />
          </Routes>

          {/* Toast notifications */}
          <Toaster
            position="top-right"
            toastOptions={{
              duration: 4000,
              style: {
                background: '#363636',
                color: '#fff',
              },
              success: {
                duration: 3000,
                theme: {
                  primary: 'green',
                  secondary: 'black',
                },
              },
            }}
          />
        </div>
      </Router>
    </AuthProvider>
  );
}

export default App;
