import axios from 'axios';

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5001';

// Create axios instance with default config
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  timeout: 30000, // 30 second timeout
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('access_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    
    // Add request timestamp for debugging
    config.metadata = { startTime: new Date() };
    console.log(`🚀 API Request: ${config.method?.toUpperCase()} ${config.url}`);
    
    return config;
  },
  (error) => {
    console.error('❌ Request interceptor error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor to handle errors and logging
api.interceptors.response.use(
  (response) => {
    const duration = new Date() - response.config.metadata.startTime;
    console.log(`✅ API Response: ${response.config.url} (${duration}ms)`, {
      status: response.status,
      data: response.data
    });
    return response;
  },
  (error) => {
    const duration = error.config?.metadata ? new Date() - error.config.metadata.startTime : 0;
    
    console.error(`❌ API Error: ${error.config?.url} (${duration}ms)`, {
      method: error.config?.method,
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data,
      message: error.message,
    });

    // Handle specific error cases
    if (error.response?.status === 401) {
      console.warn('🔐 Unauthorized - clearing auth and redirecting to login');
      localStorage.removeItem('access_token');
      localStorage.removeItem('user');
      
      // Only redirect if not already on login page
      if (!window.location.pathname.includes('/login')) {
        window.location.href = '/login';
      }
    }
    
    // Enhance error object with user-friendly messages
    const enhancedError = {
      ...error,
      userMessage: getUserFriendlyErrorMessage(error)
    };
    
    return Promise.reject(enhancedError);
  }
);

// Helper function to get user-friendly error messages
const getUserFriendlyErrorMessage = (error) => {
  if (!error.response) {
    return 'Network error. Please check your internet connection.';
  }
  
  const status = error.response.status;
  const data = error.response.data;
  
  switch (status) {
    case 400:
      return data?.message || 'Invalid request. Please check your input.';
    case 401:
      return 'Authentication required. Please log in again.';
    case 403:
      return 'Access denied. You don\'t have permission for this action.';
    case 404:
      return 'Resource not found.';
    case 409:
      return data?.message || 'Conflict. This resource already exists.';
    case 422:
      return data?.message || 'Validation error. Please check your input.';
    case 429:
      return 'Too many requests. Please try again later.';
    case 500:
      return 'Server error. Please try again later.';
    default:
      return data?.message || 'An unexpected error occurred.';
  }
};

// Auth API
export const authAPI = {
  register: (userData) => api.post('/users/register', userData),
  login: (credentials) => api.post('/users/login', credentials),
  getCurrentUser: () => api.get('/users/me'),
  logout: () => api.post('/users/logout'),
  refreshToken: () => api.post('/users/refresh'),
};

// Business API
export const businessAPI = {
  create: (businessData) => api.post('/businesses', businessData),
  getById: (businessId) => api.get(`/businesses/${businessId}`),
  update: (businessId, businessData) => api.put(`/businesses/${businessId}`, businessData),
  delete: (businessId) => api.delete(`/businesses/${businessId}`),
  getUserBusinesses: () => api.get('/businesses/user'),
  getTransferable: () => api.get('/businesses/transferable'),
  transfer: (businessId, settings = {}) => api.post('/businesses/transfer', { business_id: businessId, settings }),
  
  // Business configuration endpoints
  getConfig: (businessId) => api.get(`/businesses/${businessId}/config`),
  updateConfig: (businessId, config) => api.put(`/businesses/${businessId}/config`, config),
  
  // Business hours endpoints
  getHours: (businessId) => api.get(`/businesses/${businessId}/hours`),
  updateHours: (businessId, hours) => api.put(`/businesses/${businessId}/hours`, hours),
  
  // Business phone integration
  getPhoneConfig: (businessId) => api.get(`/businesses/${businessId}/phone`),
  updatePhoneConfig: (businessId, phoneConfig) => api.put(`/businesses/${businessId}/phone`, phoneConfig),
  testPhoneConnection: (businessId) => api.post(`/businesses/${businessId}/phone/test`),
};

// Agent Configuration API
export const agentAPI = {
  getConfig: (businessId) => api.get(`/agent-configs/${businessId}`),
  updateConfig: (businessId, config) => api.post(`/agent-configs/${businessId}`, { config }),
  testAgent: (businessId, testData) => api.post(`/agent-configs/${businessId}/test`, testData),
  getPrompts: (businessType) => api.get(`/agent-configs/prompts/${businessType}`),
  updatePrompts: (businessId, prompts) => api.put(`/agent-configs/${businessId}/prompts`, prompts),
};

// Call Logs API
export const callLogsAPI = {
  getLogs: (businessId, params = {}) => {
    const queryParams = new URLSearchParams({
      limit: params.limit || 10,
      offset: params.offset || 0,
      ...params
    });
    return api.get(`/call-logs/${businessId}?${queryParams}`);
  },
  getStats: (businessId, days = 30) => api.get(`/call-logs/${businessId}/stats?days=${days}`),
  getById: (callId) => api.get(`/call-logs/call/${callId}`),
  create: (callData) => api.post('/call-logs', callData),
  update: (callId, callData) => api.put(`/call-logs/${callId}`, callData),
  
  // Call analytics
  getAnalytics: (businessId, timeRange = '30d') => api.get(`/call-logs/${businessId}/analytics?range=${timeRange}`),
  getCallTrends: (businessId, timeRange = '30d') => api.get(`/call-logs/${businessId}/trends?range=${timeRange}`),
};

// Appointments API
export const appointmentsAPI = {
  getAppointments: (businessId, params = {}) => {
    const queryParams = new URLSearchParams({
      limit: params.limit || 20,
      offset: params.offset || 0,
      status: params.status || '',
      date_from: params.dateFrom || '',
      date_to: params.dateTo || '',
      ...params
    });
    return api.get(`/appointments/${businessId}?${queryParams}`);
  },
  getById: (appointmentId) => api.get(`/appointments/appointment/${appointmentId}`),
  create: (appointmentData) => api.post('/appointments', appointmentData),
  update: (appointmentId, appointmentData) => api.put(`/appointments/${appointmentId}`, appointmentData),
  cancel: (appointmentId, reason) => api.post(`/appointments/${appointmentId}/cancel`, { reason }),
  confirm: (appointmentId) => api.post(`/appointments/${appointmentId}/confirm`),
  
  // Appointment types/services
  getServices: (businessId) => api.get(`/appointments/${businessId}/services`),
  updateServices: (businessId, services) => api.put(`/appointments/${businessId}/services`, { services }),
};

// Calendar Integration API
export const calendarAPI = {
  // Google Calendar
  connectGoogle: (businessId, authCode) => api.post(`/calendar/${businessId}/google/connect`, { auth_code: authCode }),
  disconnectGoogle: (businessId) => api.delete(`/calendar/${businessId}/google`),
  syncGoogle: (businessId) => api.post(`/calendar/${businessId}/google/sync`),
  
  // Outlook Calendar
  connectOutlook: (businessId, authCode) => api.post(`/calendar/${businessId}/outlook/connect`, { auth_code: authCode }),
  disconnectOutlook: (businessId) => api.delete(`/calendar/${businessId}/outlook`),
  syncOutlook: (businessId) => api.post(`/calendar/${businessId}/outlook/sync`),
  
  // Calendly
  connectCalendly: (businessId, apiKey) => api.post(`/calendar/${businessId}/calendly/connect`, { api_key: apiKey }),
  disconnectCalendly: (businessId) => api.delete(`/calendar/${businessId}/calendly`),
  
  // General calendar operations
  getIntegrations: (businessId) => api.get(`/calendar/${businessId}/integrations`),
  getAvailability: (businessId, date) => api.get(`/calendar/${businessId}/availability?date=${date}`),
};

// Voice Preview API
export const previewAPI = {
  generate: (previewData) => api.post('/preview', previewData),
  getVoices: () => api.get('/preview/voices'),
  generateSample: (businessId, voice, text) => api.post('/preview/sample', { business_id: businessId, voice, text }),
};

// Analytics API
export const analyticsAPI = {
  getDashboardStats: (businessId, timeRange = '30d') => api.get(`/analytics/${businessId}/dashboard?range=${timeRange}`),
  getCallAnalytics: (businessId, timeRange = '30d') => api.get(`/analytics/${businessId}/calls?range=${timeRange}`),
  getAppointmentAnalytics: (businessId, timeRange = '30d') => api.get(`/analytics/${businessId}/appointments?range=${timeRange}`),
  getRevenueAnalytics: (businessId, timeRange = '30d') => api.get(`/analytics/${businessId}/revenue?range=${timeRange}`),
  getCustomerAnalytics: (businessId, timeRange = '30d') => api.get(`/analytics/${businessId}/customers?range=${timeRange}`),
  
  // Performance metrics
  getPerformanceMetrics: (businessId, timeRange = '30d') => api.get(`/analytics/${businessId}/performance?range=${timeRange}`),
  getAgentPerformance: (businessId, timeRange = '30d') => api.get(`/analytics/${businessId}/agent-performance?range=${timeRange}`),
};

// Health Check API
export const healthAPI = {
  check: () => api.get('/health'),
  status: () => api.get('/status'),
};

// Utility function for handling API responses
export const handleApiResponse = (response) => {
  if (response.data.success) {
    return response.data.data;
  } else {
    throw new Error(response.data.message || 'API request failed');
  }
};

// Utility function for handling API errors
export const handleApiError = (error) => {
  console.error('API Error:', error);
  
  if (error.userMessage) {
    throw new Error(error.userMessage);
  }
  
  throw new Error('An unexpected error occurred');
};

// SIP Integration API
export const sipAPI = {
  testCall: (businessId, testData) => api.post('/sip/test-call', { business_id: businessId, ...testData }),
  getConfig: () => api.get('/sip/config'),
  webhookHandler: (sipData) => api.post('/sip/webhook', sipData),
  getStatus: (businessId) => api.get(`/sip/status/${businessId}`),
};

// AI Training API
export const aiTrainingAPI = {
  getData: (businessId) => api.get(`/ai-training/${businessId}`),
  saveData: (businessId, trainingData) => api.put(`/ai-training/${businessId}`, trainingData),
  getTemplate: (businessType) => api.post('/ai-training/template', { business_type: businessType }),
  testAI: (businessId, testData) => api.post(`/ai-training/${businessId}/test`, testData),
  getBusinessTypes: () => api.get('/ai-training/business-types'),
};

// Voice Preview API
export const voiceAPI = {
  generatePreview: (voiceData) => api.post('/voice/preview', voiceData),
  getVoices: () => api.get('/voice/voices'),
  testVoice: (businessId, voiceConfig) => api.post(`/voice/${businessId}/test`, voiceConfig),
};

export default api;
