import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Building2,
  Phone,
  Mail,
  MapPin,
  Globe,
  FileText,
  ArrowLeft,
  Check,
  Plus
} from 'lucide-react';
import { Card, CardHeader, CardTitle, CardContent } from '../components/common/Card';
import Button from '../components/common/Button';
import Input from '../components/common/Input';
import { businessAPI } from '../utils/api';
import { BUSINESS_TYPES } from '../utils/constants';
import toast from 'react-hot-toast';

const NewBusiness = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    business_type: 'restaurant',
    custom_business_type: '',
    address: '',
    city: '',
    state: '',
    postal_code: '',
    phone: '',
    email: '',
    website: ''
  });

  const [errors, setErrors] = useState({});

  const validateForm = () => {
    const newErrors = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Business name is required';
    }

    if (!formData.phone.trim()) {
      newErrors.phone = 'Phone number is required';
    }

    if (!formData.email.trim()) {
      newErrors.email = 'Email address is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    if (!formData.address.trim()) {
      newErrors.address = 'Business address is required';
    }

    if (!formData.city.trim()) {
      newErrors.city = 'City is required';
    }

    if (!formData.state.trim()) {
      newErrors.state = 'State is required';
    }

    // Validate custom business type if "Other (Custom)" is selected
    if (formData.business_type === 'custom_business' && !formData.custom_business_type.trim()) {
      newErrors.custom_business_type = 'Please specify your business type';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      toast.error('Please fill in all required fields');
      return;
    }

    try {
      setLoading(true);

      console.log('🏢 Creating new business:', formData);

      const response = await businessAPI.create(formData);

      if (response.data.success) {
        toast.success(`${formData.name} has been created successfully!`);

        // Navigate back to the businesses page
        // The businesses page will automatically refresh and show the new business
        navigate('/businesses');
      } else {
        throw new Error(response.data.message || 'Failed to create business');
      }
    } catch (error) {
      console.error('❌ Error creating business:', error);
      toast.error(error.message || 'Failed to create business. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  return (
    <div className="max-w-3xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <Button
          variant="ghost"
          onClick={() => navigate('/businesses')}
          className="flex items-center space-x-2"
        >
          <ArrowLeft className="w-4 h-4" />
          <span>Back to Businesses</span>
        </Button>
      </div>

      <div>
        <h1 className="text-2xl font-bold text-secondary-900">Add New Business</h1>
        <p className="text-secondary-600">
          Add your business to start using Connecto AI voice receptionist services.
        </p>
      </div>

      {/* Form */}
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Building2 className="w-5 h-5" />
              <span>Basic Information</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="md:col-span-2">
                <Input
                  label="Business Name"
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  placeholder="Enter your business name"
                  required
                  error={errors.name}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-secondary-700 mb-2">
                  Business Type <span className="text-red-500">*</span>
                </label>
                <select
                  value={formData.business_type}
                  onChange={(e) => handleInputChange('business_type', e.target.value)}
                  className="w-full p-3 border border-secondary-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  required
                >
                  {BUSINESS_TYPES.map((type) => (
                    <option key={type.value} value={type.value}>
                      {type.label}
                    </option>
                  ))}
                </select>

                {/* Custom Business Type Input - Shows when "Other (Custom)" is selected */}
                {formData.business_type === 'custom_business' && (
                  <div className="mt-3">
                    <Input
                      label="Specify Business Type"
                      value={formData.custom_business_type}
                      onChange={(e) => handleInputChange('custom_business_type', e.target.value)}
                      placeholder="e.g., Marketing Agency, Consulting Firm, Photography Studio..."
                      required
                      error={errors.custom_business_type}
                    />
                    <p className="text-xs text-secondary-500 mt-1">
                      This will help us customize the AI voice assistant specifically for your business type.
                    </p>
                  </div>
                )}
              </div>

              <Input
                label="Phone Number"
                type="tel"
                value={formData.phone}
                onChange={(e) => handleInputChange('phone', e.target.value)}
                placeholder="+****************"
                required
                error={errors.phone}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Input
                label="Email Address"
                type="email"
                value={formData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                placeholder="<EMAIL>"
                required
                error={errors.email}
              />

              <Input
                label="Website"
                type="url"
                value={formData.website}
                onChange={(e) => handleInputChange('website', e.target.value)}
                placeholder="https://www.yourbusiness.com"
              />
            </div>

            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-secondary-700 mb-2">
                Description
              </label>
              <textarea
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                placeholder="Briefly describe your business and services..."
                rows={3}
                className="w-full p-3 border border-secondary-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              />
            </div>
          </CardContent>
        </Card>

        {/* Address Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <MapPin className="w-5 h-5" />
              <span>Business Address</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <Input
              label="Street Address"
              value={formData.address}
              onChange={(e) => handleInputChange('address', e.target.value)}
              placeholder="123 Main Street"
              required
              error={errors.address}
            />

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Input
                label="City"
                value={formData.city}
                onChange={(e) => handleInputChange('city', e.target.value)}
                placeholder="New York"
                required
                error={errors.city}
              />

              <Input
                label="State"
                value={formData.state}
                onChange={(e) => handleInputChange('state', e.target.value)}
                placeholder="NY"
                required
                error={errors.state}
              />

              <Input
                label="Postal Code"
                value={formData.postal_code}
                onChange={(e) => handleInputChange('postal_code', e.target.value)}
                placeholder="10001"
              />
            </div>
          </CardContent>
        </Card>

        {/* Next Steps Preview */}
        <Card className="border-primary-200 bg-primary-50">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Check className="w-5 h-5 text-primary-600" />
              <span>What happens next?</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3 text-sm">
              <div className="flex items-start space-x-3">
                <div className="w-6 h-6 bg-primary-600 text-white rounded-full flex items-center justify-center text-xs font-bold">
                  1
                </div>
                <div>
                  <p className="font-medium text-secondary-900">Create your business profile</p>
                  <p className="text-secondary-600">We'll set up your basic business information in our system.</p>
                </div>
              </div>

              <div className="flex items-start space-x-3">
                <div className="w-6 h-6 bg-secondary-300 text-white rounded-full flex items-center justify-center text-xs font-bold">
                  2
                </div>
                <div>
                  <p className="font-medium text-secondary-900">Configure AI Assistant</p>
                  <p className="text-secondary-600">Customize how your AI voice receptionist will represent your business.</p>
                </div>
              </div>

              <div className="flex items-start space-x-3">
                <div className="w-6 h-6 bg-secondary-300 text-white rounded-full flex items-center justify-center text-xs font-bold">
                  3
                </div>
                <div>
                  <p className="font-medium text-secondary-900">Connect Phone & Calendar</p>
                  <p className="text-secondary-600">Integrate your phone system and calendar for appointment booking.</p>
                </div>
              </div>

              <div className="flex items-start space-x-3">
                <div className="w-6 h-6 bg-secondary-300 text-white rounded-full flex items-center justify-center text-xs font-bold">
                  4
                </div>
                <div>
                  <p className="font-medium text-secondary-900">Go Live!</p>
                  <p className="text-secondary-600">Start receiving calls handled by your AI voice assistant.</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Form Actions */}
        <div className="flex justify-between items-center">
          <Button
            type="button"
            variant="outline"
            onClick={() => navigate('/businesses')}
          >
            Cancel
          </Button>

          <Button
            type="submit"
            loading={loading}
            disabled={loading}
            className="flex items-center space-x-2"
          >
            <Plus className="w-4 h-4" />
            <span>{loading ? 'Creating Business...' : 'Create Business'}</span>
          </Button>
        </div>
      </form>
    </div>
  );
};

export default NewBusiness;