import React, { useState, useEffect, useCallback, useRef } from 'react';
import { 
  Calendar, 
  Clock, 
  User, 
  Phone, 
  MapPin, 
  Plus, 
  Filter, 
  Search,
  Settings,
  CheckCircle,
  AlertCircle,
  ExternalLink,
  Zap,
  Building2,
  Users,
  TrendingUp
} from 'lucide-react';
import { Card, CardHeader, CardTitle, CardContent } from '../components/common/Card';
import Button from '../components/common/Button';
import { businessAPI } from '../utils/api';
import toast from 'react-hot-toast';

const Appointments = () => {
  const [businesses, setBusinesses] = useState([]);
  const [selectedBusiness, setSelectedBusiness] = useState(null);
  const [appointments, setAppointments] = useState([]);
  const [calendarIntegrations, setCalendarIntegrations] = useState({
    google: false,
    calendly: false
  });
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [dateRange, setDateRange] = useState('upcoming');
  const [initialized, setInitialized] = useState(false);
  
  // Use refs to prevent duplicate OAuth processing
  const oauthProcessedRef = useRef(false);
  const businessesFetchedRef = useRef(false);

  // Single initialization effect
  useEffect(() => {
    const initialize = async () => {
      // Prevent multiple initializations using state
      if (initialized) {
        console.log('⚠️ Initialization already done via state, skipping...');
        return;
      }

      console.log('🚀 Starting initialization...');
      
      // Handle OAuth callback first (only once)
      if (!oauthProcessedRef.current) {
        handleOAuthCallback();
        oauthProcessedRef.current = true;
      }
      
      // Fetch businesses (only once)  
      if (!businessesFetchedRef.current) {
        await fetchBusinesses();
        businessesFetchedRef.current = true;
      }
      
      // Mark initialization as complete
      setInitialized(true);
    };

    initialize();
  }, [initialized]); // Depend on initialized state

  // Effect for when selectedBusiness changes
  useEffect(() => {
    if (selectedBusiness) {
      console.log('📋 Selected business changed to:', selectedBusiness.id);
      loadStoredIntegrations();
      fetchAppointments();
    }
  }, [selectedBusiness?.id]); // Only depend on business ID

  // Effect to refetch appointments when calendar integrations change
  useEffect(() => {
    if (selectedBusiness && (calendarIntegrations.google || calendarIntegrations.calendly)) {
      console.log('📅 Calendar integrations changed, refetching appointments...');
      fetchAppointments();
    }
  }, [calendarIntegrations.google, calendarIntegrations.calendly, selectedBusiness?.id]);

  // Memoized function to load stored integrations
  const loadStoredIntegrations = useCallback(() => {
    if (!selectedBusiness) return;

    console.log('💾 Loading stored integrations for business:', selectedBusiness.id);
    const storedIntegrations = localStorage.getItem(`calendar_integrations_${selectedBusiness.id}`);
    
    if (storedIntegrations) {
      try {
        const parsed = JSON.parse(storedIntegrations);
        console.log('🔄 Found stored integrations:', parsed);
        setCalendarIntegrations(parsed);
      } catch {
        console.warn('Failed to parse stored integrations, using defaults');
        setCalendarIntegrations({ google: false, calendly: false });
      }
    } else {
      console.log('🔄 No stored integrations found, using defaults');
      setCalendarIntegrations({ google: false, calendly: false });
    }
  }, [selectedBusiness?.id]);

  const handleOAuthCallback = () => {
    const urlParams = new URLSearchParams(window.location.search);
    const code = urlParams.get('code');
    const state = urlParams.get('state');
    const error = urlParams.get('error');
    const errorDescription = urlParams.get('error_description');
    const isOAuthCallback = urlParams.get('oauth_callback') === 'true';
    
    console.log('🔍 OAuth Callback Debug:', { 
      url: window.location.href,
      pathname: window.location.pathname,
      code: code ? code.substring(0, 10) + '...' : null, 
      state, 
      error,
      errorDescription,
      isOAuthCallback,
      allParams: Object.fromEntries(urlParams.entries())
    });
    
    // Check if we have OAuth parameters and we're on the right path
    const hasOAuthParams = code && state;
    const isValidPath = window.location.pathname === '/appointments' || window.location.pathname === '/oauth/callback';
    
    if (hasOAuthParams && (isValidPath || isOAuthCallback)) {
      console.log('✅ OAuth callback detected, processing...');
      processOAuthCallback(code, state);
      
      // Clean up URL parameters after processing
      setTimeout(() => {
        const cleanUrl = new URL(window.location.href);
        cleanUrl.searchParams.delete('code');
        cleanUrl.searchParams.delete('state');
        cleanUrl.searchParams.delete('oauth_callback');
        cleanUrl.searchParams.delete('error');
        cleanUrl.searchParams.delete('error_description');
        window.history.replaceState({}, document.title, cleanUrl.toString());
      }, 2000);
      
    } else if (error && (isValidPath || isOAuthCallback)) {
      console.log('❌ OAuth error detected:', error, errorDescription);
      toast.error(`Calendar connection failed: ${error}${errorDescription ? ` - ${errorDescription}` : ''}`);
      
      // Clean up URL
      setTimeout(() => {
        const cleanUrl = new URL(window.location.href);
        cleanUrl.searchParams.delete('error');
        cleanUrl.searchParams.delete('error_description');
        cleanUrl.searchParams.delete('oauth_callback');
        window.history.replaceState({}, document.title, cleanUrl.toString());
      }, 1000);
      
    } else {
      console.log('ℹ️ No OAuth callback parameters found or wrong path');
    }
  };

  const fetchBusinesses = async () => {
    try {
      setLoading(true);
      console.log('🚀 Fetching businesses...');
      const response = await businessAPI.getUserBusinesses();
      const businessList = response.data.data || [];
      setBusinesses(businessList);
      
      if (businessList.length > 0) {
        console.log('📊 Setting selected business:', businessList[0].name);
        setSelectedBusiness(businessList[0]);
      }
    } catch (err) {
      console.error('Error fetching businesses:', err);
      toast.error('Failed to load businesses');
    } finally {
      setLoading(false);
    }
  };

  const fetchAppointments = async () => {
    if (!selectedBusiness) return;
    
    try {
      console.log('📅 Fetching appointments for business:', selectedBusiness.id);
      
      // Check which calendars are connected
      const connectedCalendars = [];
      if (calendarIntegrations.google) connectedCalendars.push('google');
      if (calendarIntegrations.calendly) connectedCalendars.push('calendly');
      
      console.log('📊 Connected calendars:', connectedCalendars);
      
      if (connectedCalendars.length === 0) {
        console.log('📭 No calendars connected, showing empty state');
        setAppointments([]);
        return;
      }
      
      // Fetch REAL appointments from connected calendar APIs
      let allAppointments = [];
      
      for (const provider of connectedCalendars) {
        try {
          console.log(`🔄 Fetching REAL ${provider} appointments...`);
          
          // TODO: Implement real calendar API calls
          // This is where you'll make actual API calls to your backend
          // which will then call the real calendar APIs with stored OAuth tokens
          
          if (provider === 'google') {
            // Real Google Calendar API call through your backend
            // const response = await fetch(`/api/calendar/google/events/${selectedBusiness.id}`);
            // const googleEvents = await response.json();
            // Process and transform Google Calendar events
            console.log('📅 Google Calendar: Real API integration needed');
          }
          
          if (provider === 'calendly') {
            // Real Calendly API call through your backend  
            // const response = await fetch(`/api/calendar/calendly/events/${selectedBusiness.id}`);
            // const calendlyEvents = await response.json();
            // Process and transform Calendly events
            console.log('📅 Calendly: Real API integration needed');
          }
          
          // For now, no fake data - just empty array until real integration
          
        } catch (err) {
          console.error(`❌ Failed to fetch ${provider} appointments:`, err);
          toast.error(`Failed to load ${provider} appointments`);
        }
      }
      
      console.log('📅 Total REAL appointments loaded:', allAppointments.length);
      setAppointments(allAppointments); // Will be empty until real API is implemented
      
    } catch (err) {
      console.error('Error fetching appointments:', err);
      toast.error('Failed to load appointments');
      setAppointments([]);
    }
  };

  const processOAuthCallback = async (code, state) => {
    try {
      // Parse state to get provider and business info
      const [provider, businessId] = state.split('_');
      
      console.log('🔄 Processing OAuth callback:', { provider, businessId, code: code.substring(0, 10) + '...' });
      
      // Validate that we have the business ID
      if (!businessId || !provider) {
        throw new Error('Invalid OAuth state parameter');
      }
      
      console.log(`🚀 Completing OAuth for ${provider} calendar for business ${businessId}`);
      
      toast.loading('Completing calendar connection...', { id: 'oauth-callback' });
      
      // In real implementation, this would:
      // 1. Send the authorization code to your backend
      // 2. Exchange it for access tokens
      // 3. Store tokens securely in database
      // 4. Set up calendar webhooks/subscriptions
      // 5. Update integration status
      
      // Example API call:
      // await calendarAPI.completeOAuth({
      //   provider,
      //   businessId,
      //   authorizationCode: code,
      //   redirectUri: `${window.location.origin}/oauth/callback`
      // });
      
      // Simulate successful connection
      console.log('⏳ Simulating connection process...');
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // CRITICAL FIX: Read current state from localStorage instead of using stale React state
      const currentStoredIntegrations = localStorage.getItem(`calendar_integrations_${businessId}`);
      let currentIntegrations = { google: false, calendly: false };
      
      if (currentStoredIntegrations) {
        try {
          currentIntegrations = JSON.parse(currentStoredIntegrations);
          console.log('🔍 Current stored integrations from localStorage:', currentIntegrations);
        } catch {
          console.warn('Failed to parse stored integrations, using defaults');
        }
      }
      
      console.log('🔍 Current integration state before update (from localStorage):', currentIntegrations);
      
      // Create updated integrations based on current localStorage state (not React state)
      const updatedIntegrations = {
        ...currentIntegrations, // Preserve current state from localStorage
        [provider]: true // Only update the provider we're connecting
      };
      
      console.log('🔄 Updated integrations after OAuth:', updatedIntegrations);
      
      // Update state first
      setCalendarIntegrations(updatedIntegrations);
      
      // Then persist to localStorage
      localStorage.setItem(`calendar_integrations_${businessId}`, JSON.stringify(updatedIntegrations));
      console.log('💾 Saved to localStorage:', `calendar_integrations_${businessId}`, updatedIntegrations);
      
      toast.success(`Successfully connected to ${provider} calendar! Both calendars can now work together.`, { id: 'oauth-callback' });
      
      // Clean up URL and localStorage
      window.history.replaceState({}, document.title, window.location.pathname);
      localStorage.removeItem('calendar_integration_attempt');
      
      // Refresh appointments to show synced data
      fetchAppointments();
      
    } catch (err) {
      console.error('❌ OAuth callback error:', err);
      toast.error('Failed to complete calendar connection. Please try again.', { id: 'oauth-callback' });
    }
  };

  const CalendarIntegrationCard = () => {
    const integrations = [
      {
        name: 'Google Calendar',
        key: 'google',
        icon: '📅',
        description: 'Sync with Google Calendar for seamless appointment management',
        color: 'border-blue-200 bg-blue-50',
        scopes: 'https://www.googleapis.com/auth/calendar'
      },
      {
        name: 'Calendly',
        key: 'calendly',
        icon: '🗓️',
        description: 'Integrate with Calendly for automated scheduling',
        color: 'border-green-200 bg-green-50',
        scopes: 'scheduling:read scheduling:write'
      }
    ];

    const handleConnect = async (integration) => {
      try {
        // Generate real OAuth URLs for each provider
        let authUrl = '';
        const redirectUri = import.meta.env.VITE_OAUTH_REDIRECT_URI || `${window.location.origin}/appointments`;
        const state = `${integration.key}_${selectedBusiness.id}_${Date.now()}`;
        
        console.log('🔗 OAuth Connection Debug:', {
          provider: integration.key,
          redirectUri,
          state,
          businessId: selectedBusiness.id,
          origin: window.location.origin
        });
        
        switch (integration.key) {
          case 'google': {
            // Real Google Calendar OAuth
            const googleClientId = import.meta.env.VITE_GOOGLE_CLIENT_ID || 'your-google-client-id';
            console.log('📅 Google OAuth Setup:', { googleClientId: googleClientId ? 'Set' : 'Missing' });
            authUrl = `https://accounts.google.com/o/oauth2/v2/auth?` +
              `client_id=${googleClientId}&` +
              `redirect_uri=${encodeURIComponent(redirectUri)}&` +
              `scope=${encodeURIComponent('https://www.googleapis.com/auth/calendar')}&` +
              `response_type=code&` +
              `access_type=offline&` +
              `state=${state}&` +
              `prompt=consent`;
            break;
          }
            
          case 'calendly': {
            // Real Calendly OAuth
            const calendlyClientId = import.meta.env.VITE_CALENDLY_CLIENT_ID || 'your-calendly-client-id';
            console.log('🗓️ Calendly OAuth Setup:', { calendlyClientId: calendlyClientId ? 'Set' : 'Missing' });
            authUrl = `https://auth.calendly.com/oauth/authorize?` +
              `client_id=${calendlyClientId}&` +
              `response_type=code&` +
              `redirect_uri=${encodeURIComponent(redirectUri)}&` +
              `scope=${encodeURIComponent('default')}&` +
              `state=${state}`;
            break;
          }
        }
        
        console.log('🚀 Generated OAuth URL:', authUrl);
        
        if (authUrl && !authUrl.includes('your-')) {
          // Store the integration attempt for when user returns
          localStorage.setItem('calendar_integration_attempt', JSON.stringify({
            provider: integration.key,
            businessId: selectedBusiness.id,
            timestamp: Date.now()
          }));
          
          console.log('💾 Stored integration attempt for:', integration.key);
          
          toast.loading(`Redirecting to ${integration.name}...`, { id: 'calendar-redirect' });
          
          // Short delay to show the loading message
          setTimeout(() => {
            console.log('🔄 Redirecting to OAuth provider...');
            window.location.href = authUrl;
          }, 1000);
        } else {
          throw new Error(`OAuth not configured for ${integration.name}. Please set the required environment variables.`);
        }
        
      } catch (err) {
        console.error(`Failed to connect to ${integration.name}:`, err);
        toast.error(`Failed to connect to ${integration.name}: ${err.message}`, { 
          id: 'calendar-connect',
          duration: 6000
        });
      }
    };

    const handleDisconnect = async (integration) => {
      try {
        // In real implementation, this would:
        // 1. Revoke OAuth tokens
        // 2. Remove stored calendar integration
        // 3. Clean up webhooks/subscriptions
        
        const confirmDisconnect = window.confirm(
          `Are you sure you want to disconnect ${integration.name}? This will stop syncing appointments.`
        );
        
        if (!confirmDisconnect) return;
        
        // Call API to disconnect calendar
        // await calendarAPI.disconnect(selectedBusiness.id, integration.key);
        
        const updatedIntegrations = {
          ...calendarIntegrations,
          [integration.key]: false
        };
        
        setCalendarIntegrations(updatedIntegrations);
        
        // Remove from localStorage
        localStorage.setItem(`calendar_integrations_${selectedBusiness.id}`, JSON.stringify(updatedIntegrations));
        
        toast.success(`Disconnected from ${integration.name}`);
      } catch (err) {
        console.error(`Failed to disconnect from ${integration.name}:`, err);
        toast.error(`Failed to disconnect from ${integration.name}`);
      }
    };

    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Settings className="w-5 h-5 text-primary-600" />
            <span>Calendar Integrations</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <p className="text-secondary-600 mb-4">
            Connect your business calendars so the AI can automatically book appointments and keep everything in sync.
          </p>
          
          {/* Environment Variables Warning */}
          {(!import.meta.env.VITE_GOOGLE_CLIENT_ID || !import.meta.env.VITE_CALENDLY_CLIENT_ID) && (
            <div className="bg-orange-50 border border-orange-200 text-orange-700 p-4 rounded-lg mb-4">
              <div className="flex items-center space-x-2">
                <AlertCircle className="w-5 h-5" />
                <span className="font-medium">OAuth Configuration Required</span>
              </div>
              <p className="text-sm mt-2">
                To enable real calendar connections, add your OAuth client IDs to environment variables:
              </p>
              <ul className="text-sm mt-2 space-y-1 list-disc list-inside">
                <li>VITE_GOOGLE_CLIENT_ID (Google Calendar)</li>
                <li>VITE_CALENDLY_CLIENT_ID (Calendly)</li>
              </ul>
            </div>
          )}
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {integrations.map((integration) => {
              const isConnected = calendarIntegrations[integration.key];
              
              return (
                <div
                  key={integration.key}
                  className={`p-4 rounded-lg border-2 ${integration.color}`}
                >
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex items-center space-x-3">
                      <span className="text-2xl">{integration.icon}</span>
                      <div>
                        <h3 className="font-semibold text-secondary-900">{integration.name}</h3>
                        <div className="flex items-center space-x-2 mt-1">
                          {isConnected ? (
                            <>
                              <CheckCircle className="w-4 h-4 text-green-600" />
                              <span className="text-sm text-green-700">Connected</span>
                            </>
                          ) : (
                            <>
                              <AlertCircle className="w-4 h-4 text-orange-600" />
                              <span className="text-sm text-orange-700">Not connected</span>
                            </>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <p className="text-sm text-secondary-600 mb-4">
                    {integration.description}
                  </p>
                  
                  <div className="flex items-center space-x-2">
                    {isConnected ? (
                      <>
                        <Button
                          variant="outline"
                          size="sm"
                          className="flex items-center space-x-1"
                          onClick={() => window.open(`https://${integration.key === 'google' ? 'calendar.google.com' : 'calendly.com/app'}`, '_blank')}
                        >
                          <ExternalLink className="w-3 h-3" />
                          <span>Manage</span>
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDisconnect(integration)}
                          className="text-red-600 hover:text-red-700"
                        >
                          Disconnect
                        </Button>
                      </>
                    ) : (
                      <Button
                        size="sm"
                        onClick={() => handleConnect(integration)}
                        className="flex items-center space-x-1"
                      >
                        <Zap className="w-3 h-3" />
                        <span>Connect</span>
                      </Button>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
          
          <div className="bg-blue-50 border border-blue-200 text-blue-700 p-4 rounded-lg">
            <div className="flex items-start space-x-2">
              <Settings className="w-5 h-5 mt-0.5" />
              <div>
                <h4 className="font-medium mb-1">Debug Information</h4>
                <p className="text-sm mb-2">
                  Current integration status for business: {selectedBusiness?.name || 'None selected'}
                </p>
                <div className="grid grid-cols-2 gap-2 text-xs mb-3">
                  <div>Google: {calendarIntegrations.google ? '✅ Connected' : '❌ Not connected'}</div>
                  <div>Calendly: {calendarIntegrations.calendly ? '✅ Connected' : '❌ Not connected'}</div>
                </div>
                <div className="bg-white p-2 rounded text-xs mb-3">
                  <strong>Live State:</strong> {JSON.stringify(calendarIntegrations)}
                </div>
                <div className="flex items-center space-x-2 mt-2">
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => window.open('/help/oauth-setup', '_blank')}
                  >
                    View Setup Guide
                  </Button>
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => {
                      console.log('🔍 Current calendar integrations:', calendarIntegrations);
                      console.log('💾 LocalStorage for business:', selectedBusiness?.id, localStorage.getItem(`calendar_integrations_${selectedBusiness?.id}`));
                      toast.success('Check console for debug info');
                    }}
                  >
                    Debug State
                  </Button>
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => {
                      if (selectedBusiness) {
                        localStorage.removeItem(`calendar_integrations_${selectedBusiness.id}`);
                        setCalendarIntegrations({ google: false, calendly: false });
                        toast.success('Reset integration state');
                      }
                    }}
                  >
                    Reset State
                  </Button>
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => {
                      console.log('🔧 Environment Variables Check:', {
                        googleClientId: import.meta.env.VITE_GOOGLE_CLIENT_ID ? 'Set ✅' : 'Missing ❌',
                        calendlyClientId: import.meta.env.VITE_CALENDLY_CLIENT_ID ? 'Set ✅' : 'Missing ❌',
                        oauthRedirectUri: import.meta.env.VITE_OAUTH_REDIRECT_URI || 'Using default',
                        currentOrigin: window.location.origin,
                        suggestedRedirectUri: `${window.location.origin}/appointments`
                      });
                      toast.success('Check console for environment variables info');
                    }}
                  >
                    Check Config
                  </Button>
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => {
                      if (!selectedBusiness) {
                        toast.error('Please select a business first');
                        return;
                      }
                      
                      // Force reload integrations from localStorage
                      const stored = localStorage.getItem(`calendar_integrations_${selectedBusiness.id}`);
                      if (stored) {
                        try {
                          const parsed = JSON.parse(stored);
                          setCalendarIntegrations(parsed);
                          console.log('🔄 Reloaded from localStorage:', parsed);
                          toast.success('Reloaded integrations from storage');
                        } catch {
                          toast.error('Failed to parse stored integrations');
                        }
                      } else {
                        toast.info('No stored integrations found');
                      }
                    }}
                  >
                    Reload State
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };

  const AppointmentStats = () => {
    const stats = {
      total: appointments.length,
      today: appointments.filter(apt => {
        const today = new Date();
        const aptDate = new Date(apt.date);
        return aptDate.toDateString() === today.toDateString();
      }).length,
      thisWeek: appointments.filter(apt => {
        const weekFromNow = new Date();
        weekFromNow.setDate(weekFromNow.getDate() + 7);
        const aptDate = new Date(apt.date);
        return aptDate >= new Date() && aptDate <= weekFromNow;
      }).length,
      aiBooked: appointments.filter(apt => apt.booked_by_ai).length
    };

    return (
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4 text-center">
            <Calendar className="w-8 h-8 text-primary-600 mx-auto mb-2" />
            <p className="text-2xl font-bold text-secondary-900">{stats.total}</p>
            <p className="text-sm text-secondary-600">Total Appointments</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4 text-center">
            <Clock className="w-8 h-8 text-blue-600 mx-auto mb-2" />
            <p className="text-2xl font-bold text-secondary-900">{stats.today}</p>
            <p className="text-sm text-secondary-600">Today</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4 text-center">
            <TrendingUp className="w-8 h-8 text-green-600 mx-auto mb-2" />
            <p className="text-2xl font-bold text-secondary-900">{stats.thisWeek}</p>
            <p className="text-sm text-secondary-600">This Week</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4 text-center">
            <Zap className="w-8 h-8 text-purple-600 mx-auto mb-2" />
            <p className="text-2xl font-bold text-secondary-900">{stats.aiBooked}</p>
            <p className="text-sm text-secondary-600">AI Booked</p>
          </CardContent>
        </Card>
      </div>
    );
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold text-secondary-900">Appointments</h1>
          <p className="text-secondary-600">Loading appointment system...</p>
        </div>
        <div className="animate-pulse">
          <div className="h-32 bg-secondary-200 rounded"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-secondary-900">Appointments</h1>
          <p className="text-secondary-600">
            Manage appointments booked by your AI voice assistant and integrate with your calendars
          </p>
        </div>
        
        <div className="flex items-center space-x-3">
          {businesses.length > 0 && (
            <select
              value={selectedBusiness?.id || ''}
              onChange={(e) => {
                const business = businesses.find(b => b.id === e.target.value);
                setSelectedBusiness(business);
              }}
              className="px-3 py-2 border border-secondary-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
            >
              {businesses.map((business) => (
                <option key={business.id} value={business.id}>
                  {business.name}
                </option>
              ))}
            </select>
          )}
          
          <Button className="flex items-center space-x-2">
            <Plus className="w-4 h-4" />
            <span>Manual Booking</span>
          </Button>
        </div>
      </div>

      {selectedBusiness ? (
        <>
          {/* Calendar Integration */}
          <CalendarIntegrationCard />

          {/* Appointment Statistics */}
          <div>
            <h2 className="text-lg font-semibold text-secondary-900 mb-4">Appointment Overview</h2>
            <AppointmentStats />
          </div>

          {/* Appointments List */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center space-x-2">
                  <Calendar className="w-5 h-5" />
                  <span>Recent Appointments</span>
                </CardTitle>
                
                <div className="flex items-center space-x-3">
                  <div className="relative">
                    <Search className="w-4 h-4 text-secondary-400 absolute left-3 top-1/2 transform -translate-y-1/2" />
                    <input
                      type="text"
                      placeholder="Search appointments..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10 pr-3 py-2 text-sm border border-secondary-300 rounded focus:outline-none focus:ring-2 focus:ring-primary-500"
                    />
                  </div>
                  
                  <select
                    value={dateRange}
                    onChange={(e) => setDateRange(e.target.value)}
                    className="px-3 py-2 text-sm border border-secondary-300 rounded focus:outline-none focus:ring-2 focus:ring-primary-500"
                  >
                    <option value="all">All Time</option>
                    <option value="today">Today</option>
                    <option value="upcoming">Upcoming</option>
                    <option value="past">Past</option>
                  </select>
                  
                  <select
                    value={statusFilter}
                    onChange={(e) => setStatusFilter(e.target.value)}
                    className="px-3 py-2 text-sm border border-secondary-300 rounded focus:outline-none focus:ring-2 focus:ring-primary-500"
                  >
                    <option value="all">All Status</option>
                    <option value="confirmed">Confirmed</option>
                    <option value="pending">Pending</option>
                    <option value="cancelled">Cancelled</option>
                  </select>
                </div>
              </div>
            </CardHeader>
            <CardContent className="p-0">
              {appointments.length === 0 ? (
                <div className="text-center py-12">
                  <Calendar className="w-12 h-12 text-secondary-400 mx-auto mb-4" />
                  {(calendarIntegrations.google || calendarIntegrations.calendly) ? (
                    <>
                      <h3 className="text-lg font-medium text-secondary-900 mb-2">No appointments found</h3>
                      <p className="text-secondary-600 mb-6">
                        Your calendars are connected, but no appointments were found. 
                        {calendarIntegrations.google && ' Check your Google Calendar.'}
                        {calendarIntegrations.calendly && ' Check your Calendly bookings.'}
                      </p>
                      <div className="bg-blue-50 border border-blue-200 text-blue-700 p-4 rounded-lg mb-6 max-w-md mx-auto">
                        <p className="text-sm">
                          <strong>Note:</strong> Real calendar API integration is in development. 
                          Once implemented, your actual appointments will appear here automatically.
                        </p>
                      </div>
                    </>
                  ) : (
                    <>
                      <h3 className="text-lg font-medium text-secondary-900 mb-2">No appointments yet</h3>
                      <p className="text-secondary-600 mb-6">
                        Connect your calendar above and when customers call, your AI will automatically book appointments that sync with your calendar.
                      </p>
                    </>
                  )}
                  <div className="flex items-center justify-center space-x-4">
                    <Button variant="outline" className="flex items-center space-x-2">
                      <Settings className="w-4 h-4" />
                      <span>Connect Calendar</span>
                    </Button>
                    <Button className="flex items-center space-x-2">
                      <Plus className="w-4 h-4" />
                      <span>Book Test Appointment</span>
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="space-y-0">
                  {appointments.map((appointment) => (
                    <div key={appointment.id} className="border-b border-secondary-200 p-6 hover:bg-secondary-50 transition-colors">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-3 mb-2">
                            <h3 className="text-lg font-semibold text-secondary-900">{appointment.title}</h3>
                            <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                              appointment.status === 'confirmed' ? 'bg-green-100 text-green-800' :
                              appointment.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                              appointment.status === 'cancelled' ? 'bg-red-100 text-red-800' :
                              'bg-secondary-100 text-secondary-800'
                            }`}>
                              {appointment.status}
                            </span>
                            {appointment.booked_by_ai && (
                              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                                <Zap className="w-3 h-3 mr-1" />
                                AI Booked
                              </span>
                            )}
                            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                              {appointment.provider}
                            </span>
                          </div>
                          
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-3">
                            <div className="flex items-center space-x-2 text-secondary-600">
                              <User className="w-4 h-4" />
                              <span>{appointment.customer_name}</span>
                            </div>
                            <div className="flex items-center space-x-2 text-secondary-600">
                              <Phone className="w-4 h-4" />
                              <span>{appointment.customer_phone}</span>
                            </div>
                            <div className="flex items-center space-x-2 text-secondary-600">
                              <Calendar className="w-4 h-4" />
                              <span>{new Date(appointment.date).toLocaleDateString()} at {appointment.time}</span>
                            </div>
                            <div className="flex items-center space-x-2 text-secondary-600">
                              <Clock className="w-4 h-4" />
                              <span>{appointment.duration} minutes</span>
                            </div>
                            <div className="flex items-center space-x-2 text-secondary-600">
                              <MapPin className="w-4 h-4" />
                              <span>{appointment.location}</span>
                            </div>
                            <div className="flex items-center space-x-2 text-secondary-600">
                              <span className="font-medium">{appointment.type}</span>
                            </div>
                          </div>
                          
                          {appointment.notes && (
                            <div className="bg-secondary-50 p-3 rounded-lg">
                              <p className="text-sm text-secondary-700">{appointment.notes}</p>
                            </div>
                          )}
                        </div>
                        
                        <div className="flex items-center space-x-2 ml-4">
                          <Button variant="outline" size="sm" className="flex items-center space-x-1">
                            <ExternalLink className="w-3 h-3" />
                            <span>View</span>
                          </Button>
                          <Button variant="outline" size="sm" className="flex items-center space-x-1">
                            <Settings className="w-3 h-3" />
                            <span>Edit</span>
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                  
                  {/* Load More Button */}
                  <div className="p-6 text-center border-t border-secondary-200">
                    <Button variant="outline" className="flex items-center space-x-2">
                      <Plus className="w-4 h-4" />
                      <span>Load More Appointments</span>
                    </Button>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </>
      ) : (
        <Card>
          <CardContent className="text-center py-12">
            <Building2 className="w-12 h-12 text-secondary-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-secondary-900 mb-2">No businesses found</h3>
            <p className="text-secondary-600 mb-6">
              Add a business first to set up appointment booking.
            </p>
            <Button className="flex items-center space-x-2">
              <Plus className="w-4 h-4" />
              <span>Add Business</span>
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default Appointments;