import React, { useState, useEffect } from 'react';
import { Bar<PERSON><PERSON>3, TrendingUp, Phone, Calendar, Clock, Users, ArrowUp, ArrowDown } from 'lucide-react';
import { <PERSON>, CardHeader, CardTitle, CardContent } from '../components/common/Card';
import Select from '../components/common/Select';
import LoadingSpinner from '../components/common/LoadingSpinner';
import { LineChart, Line, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell } from 'recharts';

const Analytics = () => {
  const [loading, setLoading] = useState(true);
  const [timeRange, setTimeRange] = useState('30'); // 7, 30, 90 days
  const [selectedBusiness, setSelectedBusiness] = useState('all');
  const [analyticsData, setAnalyticsData] = useState(null);

  useEffect(() => {
    fetchAnalytics();
  }, [timeRange, selectedBusiness]);

  const fetchAnalytics = async () => {
    try {
      setLoading(true);
      
      console.log('📊 Fetching REAL analytics data for:', { selectedBusiness, timeRange });
      
      // TODO: Implement real analytics API calls
      // This should call your backend which aggregates data from:
      // 1. Call logs table - for call statistics and trends
      // 2. Appointments data - for booking metrics
      // 3. Business performance metrics - for conversion rates
      // 4. AI conversation outcomes - for call success analysis
      
      try {
        // Real API calls that should be implemented:
        
        // 1. Overview Statistics - aggregate call and appointment data
        // const overviewResponse = await fetch(`/api/analytics/overview?business=${selectedBusiness}&days=${timeRange}`);
        // const overviewData = await overviewResponse.json();
        
        // 2. Call Trends - daily/weekly call volume over time
        // const trendsResponse = await fetch(`/api/analytics/trends?business=${selectedBusiness}&days=${timeRange}`);
        // const trendsData = await trendsResponse.json();
        
        // 3. Calls by Hour - hourly distribution of calls
        // const hourlyResponse = await fetch(`/api/analytics/hourly?business=${selectedBusiness}&days=${timeRange}`);
        // const hourlyData = await hourlyResponse.json();
        
        // 4. Business Performance - per-business metrics if "All Businesses" selected
        // const performanceResponse = await fetch(`/api/analytics/businesses?days=${timeRange}`);
        // const performanceData = await performanceResponse.json();
        
        // 5. Call Outcomes - categorized call results
        // const outcomesResponse = await fetch(`/api/analytics/outcomes?business=${selectedBusiness}&days=${timeRange}`);
        // const outcomesData = await outcomesResponse.json();
        
        console.log('📊 Real analytics API integration needed - no mock data');
        
        // Set to null to show proper empty states until real API is implemented
        setAnalyticsData(null);
        
      } catch (apiError) {
        console.error('❌ Failed to fetch analytics data:', apiError);
        setAnalyticsData(null);
      }
      
    } catch (error) {
      console.error('Error fetching analytics:', error);
      setAnalyticsData(null);
    } finally {
      setLoading(false);
    }
  };

  const formatNumber = (num) => {
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'k';
    }
    return num.toString();
  };

  const StatCard = ({ title, value, change, icon: Icon, format = 'number' }) => {
    const isPositive = change > 0;
    const formattedValue = format === 'percentage' ? `${value}%` : 
                          format === 'duration' ? `${value} min` : 
                          formatNumber(value);

    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-secondary-600">{title}</p>
              <p className="text-2xl font-bold text-secondary-900 mt-1">{formattedValue}</p>
              <div className={`flex items-center mt-2 text-sm ${
                isPositive ? 'text-success-600' : 'text-error-600'
              }`}>
                {isPositive ? <ArrowUp className="w-4 h-4 mr-1" /> : <ArrowDown className="w-4 h-4 mr-1" />}
                <span>{Math.abs(change)}% vs last period</span>
              </div>
            </div>
            <div className="p-3 bg-primary-100 rounded-lg">
              <Icon className="w-6 h-6 text-primary-600" />
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" text="Loading analytics..." />
      </div>
    );
  }

  // Show empty state when no real data is available
  if (!analyticsData) {
    return (
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-secondary-900">Analytics</h1>
            <p className="text-secondary-600">
              Track performance across all your AI voice assistants
            </p>
          </div>
          <div className="flex items-center gap-4">
            <Select
              value={selectedBusiness}
              onChange={(e) => setSelectedBusiness(e.target.value)}
              options={[
                { value: 'all', label: 'All Businesses' },
              ]}
            />
            <Select
              value={timeRange}
              onChange={(e) => setTimeRange(e.target.value)}
              options={[
                { value: '7', label: 'Last 7 days' },
                { value: '30', label: 'Last 30 days' },
                { value: '90', label: 'Last 90 days' }
              ]}
            />
          </div>
        </div>

        {/* Empty State */}
        <div className="text-center py-20">
          <BarChart3 className="w-16 h-16 text-secondary-400 mx-auto mb-6" />
          <h3 className="text-xl font-medium text-secondary-900 mb-4">No Analytics Data Available</h3>
          <p className="text-secondary-600 mb-6 max-w-2xl mx-auto">
            Analytics will be populated with real call data, appointment bookings, and AI performance metrics 
            once your voice assistant starts handling customer calls.
          </p>
          
          <div className="bg-blue-50 border border-blue-200 text-blue-700 p-6 rounded-lg mb-8 max-w-2xl mx-auto">
            <h4 className="font-medium mb-2">📊 Analytics will include:</h4>
            <ul className="text-sm space-y-2 text-left">
              <li>• <strong>Call Statistics:</strong> Total calls, call duration, peak hours</li>
              <li>• <strong>Appointment Metrics:</strong> Bookings made, conversion rates</li>
              <li>• <strong>AI Performance:</strong> Success rates, call outcomes, customer satisfaction</li>
              <li>• <strong>Business Insights:</strong> Trends, patterns, and optimization opportunities</li>
            </ul>
          </div>

          <div className="bg-amber-50 border border-amber-200 text-amber-700 p-4 rounded-lg max-w-xl mx-auto">
            <p className="text-sm">
              <strong>Note:</strong> Real analytics API integration is in development. 
              Data will be populated automatically from call logs and appointment records.
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-secondary-900">Analytics</h1>
          <p className="text-secondary-600">
            Track performance across all your AI voice assistants
          </p>
        </div>
        <div className="flex items-center gap-4">
          <Select
            value={selectedBusiness}
            onChange={(e) => setSelectedBusiness(e.target.value)}
            options={[
              { value: 'all', label: 'All Businesses' },
              { value: 'valencia-legal', label: 'Valencia Legal Associates' },
              { value: 'valencia-medical', label: 'Valencia Medical Center' },
              { value: 'valencia-auto', label: 'Valencia Auto Repair' },
              { value: 'valencia-dental', label: 'Valencia Dental Care' }
            ]}
          />
          <Select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value)}
            options={[
              { value: '7', label: 'Last 7 days' },
              { value: '30', label: 'Last 30 days' },
              { value: '90', label: 'Last 90 days' }
            ]}
          />
        </div>
      </div>

      {/* Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="Total Calls"
          value={analyticsData.overview.total_calls}
          change={analyticsData.overview.calls_change}
          icon={Phone}
        />
        <StatCard
          title="Appointments Booked"
          value={analyticsData.overview.total_appointments}
          change={analyticsData.overview.appointments_change}
          icon={Calendar}
        />
        <StatCard
          title="Avg Call Duration"
          value={analyticsData.overview.avg_call_duration}
          change={analyticsData.overview.duration_change}
          icon={Clock}
          format="duration"
        />
        <StatCard
          title="Conversion Rate"
          value={analyticsData.overview.conversion_rate}
          change={analyticsData.overview.conversion_change}
          icon={TrendingUp}
          format="percentage"
        />
      </div>

      {/* Charts Row */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Call Trends */}
        <Card>
          <CardHeader>
            <CardTitle>Call Trends</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={analyticsData.callTrends}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" tickFormatter={(date) => new Date(date).toLocaleDateString()} />
                <YAxis />
                <Tooltip labelFormatter={(date) => new Date(date).toLocaleDateString()} />
                <Line type="monotone" dataKey="calls" stroke="#3B82F6" strokeWidth={2} name="Calls" />
                <Line type="monotone" dataKey="appointments" stroke="#10B981" strokeWidth={2} name="Appointments" />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Calls by Hour */}
        <Card>
          <CardHeader>
            <CardTitle>Calls by Hour</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={analyticsData.callsByHour}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="hour" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="calls" fill="#3B82F6" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Business Performance & Call Outcomes */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Business Performance */}
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle>Business Performance</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {analyticsData.businessPerformance.map((business, index) => (
                <div key={index} className="flex items-center justify-between p-4 border border-secondary-200 rounded-lg">
                  <div className="flex-1">
                    <h4 className="font-medium text-secondary-900">{business.name}</h4>
                    <div className="flex items-center gap-4 mt-2 text-sm text-secondary-600">
                      <span>{business.calls} calls</span>
                      <span>{business.appointments} appointments</span>
                      <span className={`font-medium ${
                        business.conversion >= 60 ? 'text-success-600' : 
                        business.conversion >= 50 ? 'text-warning-600' : 'text-error-600'
                      }`}>
                        {business.conversion}% conversion
                      </span>
                    </div>
                  </div>
                  <div className="w-20 bg-secondary-200 rounded-full h-2">
                    <div 
                      className="bg-primary-600 h-2 rounded-full"
                      style={{ width: `${business.conversion}%` }}
                    ></div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Call Outcomes */}
        <Card>
          <CardHeader>
            <CardTitle>Call Outcomes</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={200}>
              <PieChart>
                <Pie
                  data={analyticsData.callOutcomes}
                  cx="50%"
                  cy="50%"
                  innerRadius={40}
                  outerRadius={80}
                  dataKey="value"
                >
                  {analyticsData.callOutcomes.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
            <div className="space-y-2 mt-4">
              {analyticsData.callOutcomes.map((outcome, index) => (
                <div key={index} className="flex items-center justify-between text-sm">
                  <div className="flex items-center gap-2">
                    <div 
                      className="w-3 h-3 rounded-full"
                      style={{ backgroundColor: outcome.color }}
                    ></div>
                    <span className="text-secondary-700">{outcome.name}</span>
                  </div>
                  <span className="font-medium text-secondary-900">{outcome.value}</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Insights */}
      <Card>
        <CardHeader>
          <CardTitle>Key Insights</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div className="p-4 bg-success-50 border border-success-200 rounded-lg">
              <h4 className="font-medium text-success-900 mb-2">Peak Performance</h4>
              <p className="text-sm text-success-700">
                Valencia Dental Care has the highest conversion rate at 70.4%
              </p>
            </div>
            <div className="p-4 bg-primary-50 border border-primary-200 rounded-lg">
              <h4 className="font-medium text-primary-900 mb-2">Busiest Time</h4>
              <p className="text-sm text-primary-700">
                Most calls come in between 1-3 PM daily
              </p>
            </div>
            <div className="p-4 bg-warning-50 border border-warning-200 rounded-lg">
              <h4 className="font-medium text-warning-900 mb-2">Opportunity</h4>
              <p className="text-sm text-warning-700">
                Auto repair business could improve conversion with better prompts
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default Analytics;