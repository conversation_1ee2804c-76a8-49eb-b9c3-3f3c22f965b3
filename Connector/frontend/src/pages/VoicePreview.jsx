import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { Play, Pause, Download } from 'lucide-react';
import { <PERSON>, CardHeader, CardTitle, CardContent } from '../components/common/Card';
import Button from '../components/common/Button';
import Input from '../components/common/Input';
import Select from '../components/common/Select';
import { BUSINESS_TYPES, VOICE_OPTIONS } from '../utils/constants';
import { previewAPI } from '../utils/api';
import toast from 'react-hot-toast';

const VoicePreview = () => {
  const [isGenerating, setIsGenerating] = useState(false);
  const [audioUrl, setAudioUrl] = useState(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [audioElement, setAudioElement] = useState(null);

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
  } = useForm({
    defaultValues: {
      business_type: 'restaurant',
      voice: 'shimmer',
    },
  });

  const businessType = watch('business_type');

  const onSubmit = async (data) => {
    try {
      setIsGenerating(true);
      const response = await previewAPI.generate(data);
      
      if (response.data.success) {
        const audioUrl = `${import.meta.env.VITE_API_URL}${response.data.data.audio_url}`;
        setAudioUrl(audioUrl);
        toast.success('Voice preview generated successfully!');
      } else {
        toast.error(response.data.message || 'Failed to generate preview');
      }
    } catch (error) {
      console.error('Error generating preview:', error);
      toast.error('Failed to generate voice preview');
    } finally {
      setIsGenerating(false);
    }
  };

  const handlePlayPause = () => {
    if (!audioUrl) return;

    if (audioElement) {
      if (isPlaying) {
        audioElement.pause();
      } else {
        audioElement.play();
      }
    } else {
      const audio = new Audio(audioUrl);
      audio.addEventListener('ended', () => setIsPlaying(false));
      audio.addEventListener('pause', () => setIsPlaying(false));
      audio.addEventListener('play', () => setIsPlaying(true));
      setAudioElement(audio);
      audio.play();
    }
  };

  const handleDownload = () => {
    if (!audioUrl) return;
    
    const link = document.createElement('a');
    link.href = audioUrl;
    link.download = 'voice-preview.mp3';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-secondary-900">Voice Preview</h1>
        <p className="text-secondary-600">
          Generate and listen to how your AI voice receptionist will sound to callers.
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Preview Form */}
        <Card>
          <CardHeader>
            <CardTitle>Generate Voice Preview</CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
              <Input
                label="Business Name"
                placeholder="Enter your business name"
                required
                {...register('business_name', {
                  required: 'Business name is required',
                })}
                error={errors.business_name?.message}
              />

              <Select
                label="Business Type"
                options={BUSINESS_TYPES}
                required
                {...register('business_type', {
                  required: 'Business type is required',
                })}
                error={errors.business_type?.message}
              />

              <Select
                label="Voice"
                options={VOICE_OPTIONS}
                required
                {...register('voice', {
                  required: 'Voice selection is required',
                })}
                error={errors.voice?.message}
              />

              <Input
                label="Custom Welcome Message (Optional)"
                placeholder="Leave blank to use default message for business type"
                {...register('welcome_message')}
                helperText="If provided, this will override the default welcome message"
              />

              <Button
                type="submit"
                className="w-full"
                loading={isGenerating}
                disabled={isGenerating}
              >
                Generate Preview
              </Button>
            </form>
          </CardContent>
        </Card>

        {/* Audio Player */}
        <Card>
          <CardHeader>
            <CardTitle>Audio Preview</CardTitle>
          </CardHeader>
          <CardContent>
            {audioUrl ? (
              <div className="space-y-4">
                <div className="bg-secondary-50 rounded-lg p-6 text-center">
                  <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Play className="w-8 h-8 text-primary-600" />
                  </div>
                  <p className="text-sm text-secondary-600 mb-4">
                    Your voice preview is ready!
                  </p>
                  
                  <div className="flex justify-center space-x-2">
                    <Button
                      onClick={handlePlayPause}
                      icon={isPlaying ? Pause : Play}
                      variant="primary"
                    >
                      {isPlaying ? 'Pause' : 'Play'}
                    </Button>
                    
                    <Button
                      onClick={handleDownload}
                      icon={Download}
                      variant="outline"
                    >
                      Download
                    </Button>
                  </div>
                </div>

                <audio
                  controls
                  className="w-full"
                  src={audioUrl}
                  onPlay={() => setIsPlaying(true)}
                  onPause={() => setIsPlaying(false)}
                  onEnded={() => setIsPlaying(false)}
                >
                  Your browser does not support the audio element.
                </audio>
              </div>
            ) : (
              <div className="text-center py-12">
                <div className="w-16 h-16 bg-secondary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Play className="w-8 h-8 text-secondary-400" />
                </div>
                <p className="text-secondary-600">
                  Generate a voice preview to hear how your AI receptionist will sound
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Voice Information */}
      {watch('voice') && (
        <Card>
          <CardHeader>
            <CardTitle>
              Voice: {VOICE_OPTIONS.find(voice => voice.value === watch('voice'))?.label}
            </CardTitle>
          </CardHeader>
          <CardContent>
            {(() => {
              const selectedVoice = watch('voice');
              const voiceDescriptions = {
                'alloy': 'A balanced, neutral voice suitable for all business types. Clear and professional.',
                'echo': 'A warm male voice with good clarity. Great for professional services and consulting.',
                'fable': 'A distinctive British male voice. Perfect for upscale businesses and professional services.',
                'onyx': 'A deep, authoritative male voice. Excellent for legal services, medical offices, and corporate settings.',
                'nova': 'A friendly female voice. Ideal for customer service, retail, and hospitality businesses.',
                'shimmer': 'A warm, approachable female voice. Perfect for spas, salons, and customer-facing businesses.',
                'coral': 'A warm, inviting female voice with a friendly tone. Great for restaurants, cafes, and welcoming environments.',
                'sage': 'A gentle, calming male voice. Excellent for medical offices, counseling services, and wellness centers.'
              };
              
              return (
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <p className="text-blue-800 mb-2">
                    <strong>Voice Characteristics:</strong> {voiceDescriptions[selectedVoice]}
                  </p>
                  <p className="text-blue-700 text-sm">
                    This voice will be used for all AI interactions including greetings, appointment booking, and customer service responses.
                  </p>
                </div>
              );
            })()}
          </CardContent>
        </Card>
      )}

      {/* Business Type Information */}
      {businessType && (
        <Card>
          <CardHeader>
            <CardTitle>
              {BUSINESS_TYPES.find(type => type.value === businessType)?.label} Template
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-secondary-600">
              The AI will be configured with specialized knowledge and conversation flows 
              optimized for {BUSINESS_TYPES.find(type => type.value === businessType)?.label.toLowerCase()} businesses.
              This includes industry-specific terminology, common customer inquiries, 
              and appropriate appointment scheduling workflows.
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default VoicePreview;
