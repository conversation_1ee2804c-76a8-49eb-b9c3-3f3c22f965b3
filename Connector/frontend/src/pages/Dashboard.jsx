import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import {
  Building2,
  Phone,
  TrendingUp,
  Users,
  Plus,
  ArrowRight,
  Activity,
  Download,
  Calendar,
  BarChart3,
  Settings,
  Headphones,
  AlertCircle,
  RefreshCw
} from 'lucide-react';
import { Card, CardHeader, CardTitle, CardContent } from '../components/common/Card';
import Button from '../components/common/Button';
import LoadingSpinner from '../components/common/LoadingSpinner';
import BusinessTransferModal from '../components/business/BusinessTransferModal';
import ApiTest from '../components/common/ApiTest';
import { businessAPI, callLogsAPI } from '../utils/api';
import { formatDate, formatDuration } from '../utils/helpers';
import toast from 'react-hot-toast';

const Dashboard = () => {
  const [businesses, setBusinesses] = useState([]);
  const [transferableBusinesses, setTransferableBusinesses] = useState([]);
  const [showTransferModal, setShowTransferModal] = useState(false);
  const [stats, setStats] = useState({
    totalBusinesses: 0,
    totalCalls: 0,
    avgCallDuration: 0,
    activeAgents: 0,
  });
  const [recentCalls, setRecentCalls] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async (showRefreshIndicator = false) => {
    try {
      if (showRefreshIndicator) {
        setRefreshing(true);
      } else {
        setLoading(true);
      }
      setError(null);

      console.log('🔄 Fetching dashboard data...');

      // Check if user is authenticated
      const token = localStorage.getItem('access_token');
      const user = localStorage.getItem('user');
      console.log('🔐 Auth check:', {
        hasToken: !!token,
        tokenLength: token?.length,
        hasUser: !!user,
        userInfo: user ? JSON.parse(user) : null
      });

      if (!token) {
        throw new Error('No authentication token found. Please log in again.');
      }

      // Fetch user's businesses
      console.log('📞 Making API call to /businesses/user...');
      const businessResponse = await businessAPI.getUserBusinesses();
      
      console.log('📊 Raw API response:', {
        status: businessResponse.status,
        headers: businessResponse.headers,
        data: businessResponse.data
      });

      // Handle different response formats
      let businessData = [];
      if (businessResponse.data) {
        if (businessResponse.data.success) {
          businessData = businessResponse.data.data || [];
          console.log('✅ Success response format detected');
        } else if (Array.isArray(businessResponse.data)) {
          businessData = businessResponse.data;
          console.log('📋 Direct array response format detected');
        } else if (businessResponse.data.data) {
          businessData = businessResponse.data.data;
          console.log('📋 Nested data response format detected');
        } else {
          console.warn('⚠️ Unexpected response format:', businessResponse.data);
          businessData = [];
        }
      }
      
      console.log('📊 Processed businesses:', {
        count: businessData.length,
        businesses: businessData
      });
      
      setBusinesses(businessData);

      // Check for transferable businesses if user has no businesses in Connecto
      if (businessData.length === 0) {
        try {
          console.log('🔍 No businesses found, checking for transferable businesses...');
          const transferableResponse = await businessAPI.getTransferable();
          
          console.log('📋 Transferable response:', transferableResponse.data);
          
          let transferableData = [];
          if (transferableResponse.data) {
            if (transferableResponse.data.success) {
              transferableData = transferableResponse.data.data || [];
            } else if (Array.isArray(transferableResponse.data)) {
              transferableData = transferableResponse.data;
            }
          }
          
          console.log('📋 Transferable businesses found:', transferableData.length);
          setTransferableBusinesses(transferableData);

          // Auto-show transfer modal if there are transferable businesses
          if (transferableData.length > 0) {
            setShowTransferModal(true);
          }
        } catch (transferError) {
          console.warn('⚠️ Could not fetch transferable businesses:', {
            error: transferError.message,
            response: transferError.response?.data,
            status: transferError.response?.status
          });
          // Don't show error for transferable businesses as it's optional
        }
      }

      // Calculate stats and fetch call data
      await fetchStatsAndCalls(businessData);

    } catch (error) {
      console.error('❌ Error fetching dashboard data:', {
        message: error.message,
        response: error.response?.data,
        status: error.response?.status,
        stack: error.stack
      });
      
      let errorMessage = error.message;
      if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error.userMessage) {
        errorMessage = error.userMessage;
      }
      
      setError(errorMessage);
      toast.error(`Failed to load dashboard: ${errorMessage}`);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const fetchStatsAndCalls = async (businessData) => {
    const totalBusinesses = businessData.length;
    let totalCalls = 0;
    let totalDuration = 0;
    const recentCallsData = [];

    if (businessData.length === 0) {
      setStats({
        totalBusinesses: 0,
        totalCalls: 0,
        avgCallDuration: 0,
        activeAgents: 0,
      });
      setRecentCalls([]);
      return;
    }

    // Fetch call stats for each business
    const statsPromises = businessData.map(async (business) => {
      try {
        console.log(`📞 Fetching call stats for business: ${business.name}`);
        
        // Fetch call statistics
        const callStatsResponse = await callLogsAPI.getStats(business.id, 30);
        const callStats = callStatsResponse.data.success ? callStatsResponse.data.data : {};
        
        // Fetch recent calls
        const callLogsResponse = await callLogsAPI.getLogs(business.id, { limit: 5 });
        const callLogs = callLogsResponse.data.success ? callLogsResponse.data.data : [];

        return {
          business,
          stats: callStats,
          calls: callLogs.map(call => ({
            ...call,
            business_name: business.name,
          }))
        };
      } catch (error) {
        console.warn(`⚠️ Call stats not available for business ${business.name}:`, error.message);
        // Return empty data for businesses without call stats (expected for new businesses)
        return {
          business,
          stats: { total_calls: 0, total_duration: 0 },
          calls: []
        };
      }
    });

    try {
      const results = await Promise.allSettled(statsPromises);
      
      results.forEach((result) => {
        if (result.status === 'fulfilled') {
          const { stats: businessStats, calls } = result.value;
          totalCalls += businessStats.total_calls || 0;
          totalDuration += businessStats.total_duration || 0;
          recentCallsData.push(...calls);
        }
      });

      setStats({
        totalBusinesses,
        totalCalls,
        avgCallDuration: totalCalls > 0 ? Math.round(totalDuration / totalCalls) : 0,
        activeAgents: businessData.filter(b => b.is_active).length,
      });

      // Sort recent calls by timestamp and take the most recent
      const sortedCalls = recentCallsData
        .sort((a, b) => new Date(b.timestamp || b.created_at) - new Date(a.timestamp || a.created_at))
        .slice(0, 5);
      
      setRecentCalls(sortedCalls);
      
      console.log('📈 Dashboard stats calculated:', {
        totalBusinesses,
        totalCalls,
        avgCallDuration: totalCalls > 0 ? Math.round(totalDuration / totalCalls) : 0,
        recentCalls: sortedCalls.length
      });

    } catch (error) {
      console.error('❌ Error processing stats and calls:', error);
      // Set default stats if processing fails
      setStats({
        totalBusinesses,
        totalCalls: 0,
        avgCallDuration: 0,
        activeAgents: businessData.filter(b => b.is_active).length,
      });
      setRecentCalls([]);
    }
  };

  const handleTransferComplete = (transferredBusinesses) => {
    console.log('✅ Business transfer completed:', transferredBusinesses.length);
    toast.success(`Successfully transferred ${transferredBusinesses.length} business(es)!`);
    
    // Refresh the dashboard data after successful transfer
    fetchDashboardData();
    setShowTransferModal(false);
  };

  const handleRefresh = () => {
    fetchDashboardData(true);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" text="Loading dashboard..." />
      </div>
    );
  }

  if (error && businesses.length === 0) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-secondary-900">Dashboard</h1>
            <p className="text-secondary-600">Welcome back to Connecto AI Voice Receptionist</p>
          </div>
          <Button onClick={handleRefresh} icon={RefreshCw} loading={refreshing}>
            Retry
          </Button>
        </div>
        
        <Card>
          <CardContent className="text-center py-12">
            <AlertCircle className="w-12 h-12 text-error-500 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-secondary-900 mb-2">Failed to Load Dashboard</h3>
            <p className="text-secondary-600 mb-4">{error}</p>
            <Button onClick={handleRefresh} loading={refreshing}>
              Try Again
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-secondary-900">Dashboard</h1>
          <p className="text-secondary-600">
            Welcome back! Here's what's happening with your AI voice receptionists.
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            onClick={handleRefresh}
            icon={RefreshCw}
            loading={refreshing}
            size="sm"
          >
            Refresh
          </Button>
          <Button
            as={Link}
            to="/businesses/new"
            icon={Plus}
          >
            Add Business
          </Button>
        </div>
      </div>

      {/* Error Banner (if there's an error but we have some data) */}
      {error && businesses.length > 0 && (
        <Card className="border-warning-200 bg-warning-50">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <AlertCircle className="w-5 h-5 text-warning-600" />
              <div className="flex-1">
                <p className="text-warning-800 font-medium">Some data may be incomplete</p>
                <p className="text-warning-700 text-sm">{error}</p>
              </div>
              <Button variant="outline" size="sm" onClick={handleRefresh}>
                Retry
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-2 bg-primary-100 rounded-lg">
                <Building2 className="w-6 h-6 text-primary-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-secondary-600">Total Businesses</p>
                <p className="text-2xl font-bold text-secondary-900">{stats.totalBusinesses}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-2 bg-success-100 rounded-lg">
                <Phone className="w-6 h-6 text-success-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-secondary-600">Total Calls</p>
                <p className="text-2xl font-bold text-secondary-900">{stats.totalCalls}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-2 bg-warning-100 rounded-lg">
                <TrendingUp className="w-6 h-6 text-warning-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-secondary-600">Avg Call Duration</p>
                <p className="text-2xl font-bold text-secondary-900">
                  {formatDuration(stats.avgCallDuration)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-2 bg-purple-100 rounded-lg">
                <Activity className="w-6 h-6 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-secondary-600">Active Agents</p>
                <p className="text-2xl font-bold text-secondary-900">{stats.activeAgents}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions for Integration Features */}
      {businesses.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Setup Your Business Integrations</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <Button
                variant="outline"
                className="h-20 flex-col justify-center"
                as={Link}
                to="/appointments"
              >
                <Calendar className="w-6 h-6 mb-2 text-primary-600" />
                <span className="text-sm">Manage Appointments</span>
              </Button>
              <Button
                variant="outline"
                className="h-20 flex-col justify-center"
                as={Link}
                to="/analytics"
              >
                <BarChart3 className="w-6 h-6 mb-2 text-primary-600" />
                <span className="text-sm">View Analytics</span>
              </Button>
              <Button
                variant="outline"
                className="h-20 flex-col justify-center"
                as={Link}
                to="/preview"
              >
                <Headphones className="w-6 h-6 mb-2 text-primary-600" />
                <span className="text-sm">Test AI Voice</span>
              </Button>
              <Button
                variant="outline"
                className="h-20 flex-col justify-center"
                as={Link}
                to={businesses.length > 0 ? `/businesses/${businesses[0].id}` : '/businesses'}
              >
                <Settings className="w-6 h-6 mb-2 text-primary-600" />
                <span className="text-sm">Configure Business</span>
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Businesses */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle>Your Businesses</CardTitle>
              <Button
                variant="ghost"
                size="sm"
                as={Link}
                to="/businesses"
                icon={ArrowRight}
                iconPosition="right"
              >
                View all
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            {businesses.length === 0 ? (
              <div className="text-center py-8">
                <Building2 className="w-12 h-12 text-secondary-400 mx-auto mb-4" />
                <p className="text-secondary-600 mb-4">No businesses yet</p>
                <div className="space-y-2">
                  <Button
                    as={Link}
                    to="/businesses/new"
                    size="sm"
                    icon={Plus}
                  >
                    Add your first business
                  </Button>
                  {transferableBusinesses.length > 0 && (
                    <Button
                      variant="outline"
                      size="sm"
                      icon={Download}
                      onClick={() => setShowTransferModal(true)}
                    >
                      Transfer from other apps ({transferableBusinesses.length})
                    </Button>
                  )}
                </div>
              </div>
            ) : (
              <div className="space-y-4">
                {businesses.slice(0, 3).map((business) => (
                  <div
                    key={business.id}
                    className="flex items-center justify-between p-3 border border-secondary-200 rounded-lg hover:bg-secondary-50 transition-colors"
                  >
                    <div>
                      <h4 className="font-medium text-secondary-900">{business.name}</h4>
                      <p className="text-sm text-secondary-600 capitalize">
                        {business.business_type?.replace('_', ' ') || 'Business'}
                      </p>
                      <div className="flex items-center gap-2 mt-1">
                        <span className={`px-2 py-1 text-xs rounded-full ${
                          business.is_active 
                            ? 'bg-success-100 text-success-800' 
                            : 'bg-secondary-100 text-secondary-800'
                        }`}>
                          {business.is_active ? 'Active' : 'Inactive'}
                        </span>
                      </div>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      as={Link}
                      to={`/businesses/${business.id}`}
                    >
                      View
                    </Button>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Recent Calls */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle>Recent Calls</CardTitle>
              <Button
                variant="ghost"
                size="sm"
                as={Link}
                to="/call-logs"
                icon={ArrowRight}
                iconPosition="right"
              >
                View all
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            {recentCalls.length === 0 ? (
              <div className="text-center py-8">
                <Phone className="w-12 h-12 text-secondary-400 mx-auto mb-4" />
                <p className="text-secondary-600">No calls yet</p>
                <p className="text-sm text-secondary-500 mt-1">
                  {businesses.length === 0 
                    ? 'Add a business to start receiving calls'
                    : 'Calls will appear here once your AI starts handling them'
                  }
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                {recentCalls.map((call) => (
                  <div
                    key={call.id}
                    className="flex items-center justify-between p-3 border border-secondary-200 rounded-lg"
                  >
                    <div>
                      <h4 className="font-medium text-secondary-900">
                        {call.caller_number || call.caller_phone || 'Unknown'}
                      </h4>
                      <p className="text-sm text-secondary-600">{call.business_name}</p>
                      <p className="text-xs text-secondary-500">
                        {formatDate(call.timestamp || call.created_at, 'MMM dd, h:mm a')}
                      </p>
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-medium text-secondary-900">
                        {formatDuration(call.duration || 0)}
                      </p>
                      {call.status && (
                        <span className={`px-2 py-1 text-xs rounded-full ${
                          call.status === 'completed' ? 'bg-success-100 text-success-800' :
                          call.status === 'failed' ? 'bg-error-100 text-error-800' :
                          'bg-warning-100 text-warning-800'
                        }`}>
                          {call.status}
                        </span>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Business Transfer Modal */}
      <BusinessTransferModal
        isOpen={showTransferModal}
        onClose={() => setShowTransferModal(false)}
        businesses={transferableBusinesses}
        onTransferComplete={handleTransferComplete}
      />

      {/* Debug Panel - Remove this in production */}
      {import.meta.env.DEV && <ApiTest />}
    </div>
  );
};

export default Dashboard;
