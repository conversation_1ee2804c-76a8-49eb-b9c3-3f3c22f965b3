import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { 
  Phone, 
  PhoneCall, 
  PhoneIncoming, 
  PhoneOutgoing, 
  PhoneMissed,
  Settings,
  Plus,
  Play,
  Pause,
  Download,
  Filter,
  Search,
  Calendar,
  Clock,
  User,
  BarChart3,
  TrendingUp,
  TrendingDown,
  Zap,
  CheckCircle,
  AlertCircle,
  Building2,
  Headphones,
  ArrowRight,
  PhoneOff,
  Volume2,
  Mic
} from 'lucide-react';
import { Card, CardHeader, CardTitle, CardContent } from '../components/common/Card';
import Button from '../components/common/Button';
import { businessAPI, callLogsAPI, sipAPI } from '../utils/api';
import toast from 'react-hot-toast';

const CallLogs = () => {
  const [businesses, setBusinesses] = useState([]);
  const [selectedBusiness, setSelectedBusiness] = useState(null);
  const [callLogs, setCallLogs] = useState([]);
  const [callStats, setCallStats] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [dateRange, setDateRange] = useState('7d');
  const [isTesting, setIsTesting] = useState(false);

  useEffect(() => {
    fetchBusinesses();
  }, []);

  useEffect(() => {
    if (selectedBusiness) {
      fetchCallLogs();
      fetchCallStats();
    }
  }, [selectedBusiness, dateRange, statusFilter]);

  const fetchBusinesses = async () => {
    try {
      setLoading(true);
      const response = await businessAPI.getUserBusinesses();
      const businessList = response.data.data || [];
      setBusinesses(businessList);
      
      if (businessList.length > 0) {
        setSelectedBusiness(businessList[0]);
      }
    } catch (err) {
      setError('Failed to load businesses');
    } finally {
      setLoading(false);
    }
  };

  const fetchCallLogs = async () => {
    if (!selectedBusiness) return;
    
    try {
      const response = await callLogsAPI.getLogs(selectedBusiness.id, {
        status: statusFilter === 'all' ? '' : statusFilter,
        limit: 50,
        offset: 0
      });
      setCallLogs(response.data.data || []);
    } catch (err) {
      console.error('Error fetching call logs:', err);
      setCallLogs([]);
    }
  };

  const fetchCallStats = async () => {
    if (!selectedBusiness) return;
    
    try {
      const days = parseInt(dateRange.replace('d', ''));
      const response = await callLogsAPI.getStats(selectedBusiness.id, days);
      setCallStats(response.data.data || null);
    } catch (err) {
      console.error('Error fetching call stats:', err);
      setCallStats(null);
    }
  };

  const PhoneSetupCard = ({ business }) => {
    const [phoneConnected, setPhoneConnected] = useState(!!business?.phone);
    const [businessPhone, setBusinessPhone] = useState(business?.phone || '');
    const [saving, setSaving] = useState(false);
    const [saveError, setSaveError] = useState(null);
    
    const handleConnectPhone = async () => {
      if (!businessPhone.trim()) {
        setSaveError('Please enter a valid phone number');
        return;
      }
      
      try {
        setSaving(true);
        setSaveError(null);
        
        // Update business phone number via phone config API
        await businessAPI.updatePhoneConfig(business.id, { phone: businessPhone.trim() });
        
        console.log('✅ Phone number saved:', businessPhone);
        setPhoneConnected(true);
        
        // Update the selected business to reflect the change
        setSelectedBusiness({
          ...selectedBusiness,
          phone: businessPhone.trim()
        });
        
        // Refresh businesses list to keep in sync
        fetchBusinesses();
        
        toast.success('Phone number connected successfully!');
        
      } catch (err) {
        console.error('❌ Failed to save phone number:', err);
        setSaveError('Failed to save phone number. Please try again.');
      } finally {
        setSaving(false);
      }
    };

    const handleDisconnectPhone = async () => {
      try {
        setSaving(true);
        setSaveError(null);
        
        // Remove phone number via phone config API
        await businessAPI.updatePhoneConfig(business.id, { phone: null });
        
        console.log('📞 Phone disconnected');
        setPhoneConnected(false);
        setBusinessPhone('');
        
        // Update the selected business
        setSelectedBusiness({
          ...selectedBusiness,
          phone: null
        });
        
        // Refresh businesses list
        fetchBusinesses();
        
        toast.success('Phone disconnected successfully');
        
      } catch (err) {
        console.error('❌ Failed to disconnect phone:', err);
        setSaveError('Failed to disconnect phone. Please try again.');
      } finally {
        setSaving(false);
      }
    };

    const handleTestCall = async () => {
      if (!selectedBusiness?.phone) {
        toast.error('Please connect a phone number first');
        return;
      }

      setIsTesting(true);
      
      try {
        const loadingToast = toast.loading('Testing SIP integration...');
        
        // Test the SIP integration
        const response = await sipAPI.testCall(selectedBusiness.id, {
          test_number: selectedBusiness.phone // Use the actual business phone number
        });

        toast.dismiss(loadingToast);

        if (response.data.success) {
          toast.success('SIP integration test successful! LiveKit room created and ready for voice agent.');
          
          // Refresh call logs to show the test call
          await Promise.all([
            fetchCallLogs(),
            fetchCallStats()
          ]);
          
          // Show technical details in console for debugging
          console.log('SIP Test Details:', response.data);
          
        } else {
          toast.error(`SIP test failed: ${response.data.message || response.data.error}`);
          console.error('SIP test error:', response.data);
        }
        
      } catch (error) {
        console.error('Error testing SIP integration:', error);
        toast.error('SIP integration test failed. Please check your phone system configuration.');
      } finally {
        setIsTesting(false);
      }
    };

    return (
      <Card className="border-primary-200 bg-gradient-to-r from-primary-50 to-blue-50">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Phone className="w-5 h-5 text-primary-600" />
            <span>Phone System Integration</span>
            {phoneConnected ? (
              <CheckCircle className="w-5 h-5 text-green-600" />
            ) : (
              <AlertCircle className="w-5 h-5 text-orange-600" />
            )}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {saveError && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
              {saveError}
            </div>
          )}
          
          {!phoneConnected ? (
            <div>
              <h3 className="font-semibold text-secondary-900 mb-3">Connect Your Business Phone</h3>
              <p className="text-sm text-secondary-600 mb-4">
                Connect your business phone number to Connecto AI so we can answer calls, book appointments, and help your customers.
              </p>
              
              <div className="space-y-3">
                <div>
                  <label className="block text-sm font-medium text-secondary-700 mb-1">
                    Business Phone Number
                  </label>
                  <input
                    type="tel"
                    value={businessPhone}
                    onChange={(e) => setBusinessPhone(e.target.value)}
                    placeholder="+****************"
                    className="w-full px-3 py-2 border border-secondary-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                    disabled={saving}
                  />
                </div>
                
                <div className="flex space-x-3">
                  <Button 
                    onClick={handleConnectPhone}
                    disabled={saving || !businessPhone.trim()}
                    className="flex items-center space-x-2"
                  >
                    <Zap className="w-4 h-4" />
                    <span>{saving ? 'Saving...' : 'Connect Phone'}</span>
                  </Button>
                  
                  <Button variant="outline" className="flex items-center space-x-2">
                    <Settings className="w-4 h-4" />
                    <span>Advanced Setup</span>
                  </Button>
                </div>
              </div>
            </div>
          ) : (
            <div className="bg-white rounded-lg p-4 border border-green-200">
              <div className="flex items-center space-x-3 mb-3">
                <CheckCircle className="w-6 h-6 text-green-600" />
                <div>
                  <h3 className="font-semibold text-secondary-900">Phone Connected</h3>
                  <p className="text-sm text-secondary-600">{businessPhone}</p>
                </div>
              </div>
              
              <div className="grid grid-cols-3 gap-3 mb-4">
                <div className="text-center p-2 bg-green-50 rounded">
                  <PhoneCall className="w-5 h-5 text-green-600 mx-auto mb-1" />
                  <p className="text-xs text-green-700">AI Active</p>
                </div>
                <div className="text-center p-2 bg-blue-50 rounded">
                  <Headphones className="w-5 h-5 text-blue-600 mx-auto mb-1" />
                  <p className="text-xs text-blue-700">Listening</p>
                </div>
                <div className="text-center p-2 bg-purple-50 rounded">
                  <Mic className="w-5 h-5 text-purple-600 mx-auto mb-1" />
                  <p className="text-xs text-purple-700">Ready</p>
                </div>
              </div>
              
              <div className="flex space-x-2">
                <Button variant="outline" size="sm" className="flex items-center space-x-1">
                  <Settings className="w-3 h-3" />
                  <span>Settings</span>
                </Button>
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="flex items-center space-x-1"
                  onClick={handleTestCall}
                  disabled={isTesting || saving}
                >
                  <Volume2 className="w-3 h-3" />
                  <span>{isTesting ? 'Testing...' : 'Test Call'}</span>
                </Button>
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="flex items-center space-x-1"
                  onClick={handleDisconnectPhone}
                  disabled={saving}
                >
                  <PhoneOff className="w-3 h-3" />
                  <span>{saving ? 'Saving...' : 'Disconnect'}</span>
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    );
  };

  const StatsCards = ({ stats }) => {
    if (!stats) return null;

    const statItems = [
      {
        label: 'Total Calls',
        value: stats.total_calls,
        icon: PhoneCall,
        color: 'blue',
        change: '+12%'
      },
      {
        label: 'AI Handled',
        value: `${Math.round((stats.ai_handled / stats.total_calls) * 100)}%`,
        icon: Headphones,
        color: 'green',
        change: '+5%'
      },
      {
        label: 'Avg Duration',
        value: `${Math.floor(stats.average_duration / 60)}:${(stats.average_duration % 60).toString().padStart(2, '0')}`,
        icon: Clock,
        color: 'purple',
        change: '-2%'
      },
      {
        label: 'Appointments',
        value: stats.appointments_booked,
        icon: Calendar,
        color: 'orange',
        change: '+18%'
      }
    ];

    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {statItems.map((item, idx) => (
          <Card key={idx}>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-secondary-600">{item.label}</p>
                  <p className="text-2xl font-bold text-secondary-900">{item.value}</p>
                  <div className="flex items-center mt-1">
                    {item.change.startsWith('+') ? (
                      <TrendingUp className="w-3 h-3 text-green-600 mr-1" />
                    ) : (
                      <TrendingDown className="w-3 h-3 text-red-600 mr-1" />
                    )}
                    <span className={`text-xs ${item.change.startsWith('+') ? 'text-green-600' : 'text-red-600'}`}>
                      {item.change}
                    </span>
                  </div>
                </div>
                <div className={`w-10 h-10 rounded-lg flex items-center justify-center bg-${item.color}-100`}>
                  <item.icon className={`w-5 h-5 text-${item.color}-600`} />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  };

  const CallLogItem = ({ call }) => {
    const getStatusIcon = (status) => {
      switch (status) {
        case 'completed': return <PhoneIncoming className="w-4 h-4 text-green-600" />;
        case 'transferred': return <ArrowRight className="w-4 h-4 text-blue-600" />;
        case 'missed': return <PhoneMissed className="w-4 h-4 text-red-600" />;
        default: return <PhoneCall className="w-4 h-4 text-secondary-600" />;
      }
    };

    const formatDuration = (seconds) => {
      const mins = Math.floor(seconds / 60);
      const secs = seconds % 60;
      return `${mins}:${secs.toString().padStart(2, '0')}`;
    };

    return (
      <div className="flex items-center justify-between p-4 border-b border-secondary-100 hover:bg-secondary-50">
        <div className="flex items-center space-x-4">
          <div className="flex items-center justify-center w-8 h-8 rounded-full bg-secondary-100">
            {getStatusIcon(call.status)}
          </div>
          
          <div>
            <div className="flex items-center space-x-2">
              <p className="font-medium text-secondary-900">
                {call.caller_name || 'Unknown Caller'}
              </p>
              {call.ai_handled && (
                <span className="px-2 py-0.5 text-xs bg-primary-100 text-primary-700 rounded">
                  AI Handled
                </span>
              )}
              {call.transferred && (
                <span className="px-2 py-0.5 text-xs bg-blue-100 text-blue-700 rounded">
                  Transferred
                </span>
              )}
            </div>
            <p className="text-sm text-secondary-600">{call.phone_number || 'Unknown Number'}</p>
            <p className="text-sm text-secondary-500">{call.summary || 'No summary available'}</p>
          </div>
        </div>
        
        <div className="text-right">
          <p className="text-sm text-secondary-900">
            {formatDuration(call.duration || 0)}
          </p>
          <p className="text-xs text-secondary-500">
            {call.timestamp ? new Date(call.timestamp).toLocaleTimeString() : 'Unknown time'}
          </p>
          <div className="flex items-center space-x-1 mt-1">
            <Button variant="outline" size="sm" className="p-1">
              <Play className="w-3 h-3" />
            </Button>
            <Button variant="outline" size="sm" className="p-1">
              <Download className="w-3 h-3" />
            </Button>
          </div>
        </div>
      </div>
    );
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold text-secondary-900">Call Management</h1>
          <p className="text-secondary-600">Loading phone system...</p>
        </div>
        <div className="animate-pulse">
          <div className="h-32 bg-secondary-200 rounded"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-secondary-900">Call Management</h1>
          <p className="text-secondary-600">
            Manage phone integration, monitor calls, and analyze AI performance for your business.
          </p>
        </div>
        
        <div className="flex items-center space-x-3">
          <select
            value={selectedBusiness?.id || ''}
            onChange={(e) => {
              const business = businesses.find(b => b.id === e.target.value);
              setSelectedBusiness(business);
            }}
            className="px-3 py-2 border border-secondary-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
          >
            {businesses.map((business) => (
              <option key={business.id} value={business.id}>
                {business.name}
              </option>
            ))}
          </select>
          
          <Link to="/businesses">
            <Button variant="outline" className="flex items-center space-x-2">
              <Settings className="w-4 h-4" />
              <span>Configure AI</span>
            </Button>
          </Link>
        </div>
      </div>

      {selectedBusiness && (
        <>
          {/* Phone Setup */}
          <PhoneSetupCard business={selectedBusiness} />

          {/* Call Statistics */}
          <div>
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-semibold text-secondary-900">Call Statistics</h2>
              <select
                value={dateRange}
                onChange={(e) => setDateRange(e.target.value)}
                className="px-3 py-1 text-sm border border-secondary-300 rounded focus:outline-none focus:ring-2 focus:ring-primary-500"
              >
                <option value="7d">Last 7 days</option>
                <option value="30d">Last 30 days</option>
                <option value="90d">Last 90 days</option>
              </select>
            </div>
            <StatsCards stats={callStats} />
          </div>

          {/* Call Logs */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center space-x-2">
                  <PhoneCall className="w-5 h-5" />
                  <span>Recent Calls</span>
                </CardTitle>
                
                <div className="flex items-center space-x-3">
                  <div className="relative">
                    <Search className="w-4 h-4 text-secondary-400 absolute left-3 top-1/2 transform -translate-y-1/2" />
                    <input
                      type="text"
                      placeholder="Search calls..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10 pr-3 py-2 text-sm border border-secondary-300 rounded focus:outline-none focus:ring-2 focus:ring-primary-500"
                    />
                  </div>
                  
                  <select
                    value={statusFilter}
                    onChange={(e) => setStatusFilter(e.target.value)}
                    className="px-3 py-2 text-sm border border-secondary-300 rounded focus:outline-none focus:ring-2 focus:ring-primary-500"
                  >
                    <option value="all">All Calls</option>
                    <option value="completed">Completed</option>
                    <option value="transferred">Transferred</option>
                    <option value="missed">Missed</option>
                  </select>
                </div>
              </div>
            </CardHeader>
            <CardContent className="p-0">
              {callLogs.length === 0 ? (
                <div className="text-center py-12">
                  <PhoneCall className="w-12 h-12 text-secondary-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-secondary-900 mb-2">No calls yet</h3>
                  <p className="text-secondary-600 mb-4">
                    Once your phone is connected, all calls will appear here.
                  </p>
                </div>
              ) : (
                <div>
                  {callLogs
                    .filter(call => 
                      call.caller_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                      call.phone_number?.includes(searchTerm)
                    )
                    .map((call) => (
                      <CallLogItem key={call.id} call={call} />
                    ))
                  }
                </div>
              )}
            </CardContent>
          </Card>
        </>
      )}

      {businesses.length === 0 && (
        <Card>
          <CardContent className="text-center py-12">
            <Building2 className="w-12 h-12 text-secondary-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-secondary-900 mb-2">No businesses found</h3>
            <p className="text-secondary-600 mb-6">
              Add a business first to set up phone integration.
            </p>
            <Link to="/businesses">
              <Button className="flex items-center space-x-2">
                <Plus className="w-4 h-4" />
                <span>Add Business</span>
              </Button>
            </Link>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default CallLogs;
