import React, { useState, useEffect } from 'react';
import { 
  Headphones, 
  MessageSquare, 
  BookOpen, 
  Zap, 
  Plus, 
  Edit, 
  Trash2, 
  Play, 
  Save,
  Building2,
  Brain,
  FileText,
  Users,
  Phone,
  Calendar,
  Settings,
  ArrowRight,
  CheckCircle,
  AlertCircle,
  Upload,
  Download
} from 'lucide-react';
import { Card, CardHeader, CardTitle, CardContent } from '../components/common/Card';
import Button from '../components/common/Button';
import Input from '../components/common/Input';
import { businessAPI, aiTrainingAPI } from '../utils/api';
import toast from 'react-hot-toast';

const AITraining = () => {
  const [businesses, setBusinesses] = useState([]);
  const [selectedBusiness, setSelectedBusiness] = useState(null);
  const [activeSection, setActiveSection] = useState('overview');
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  
  // Training data states
  const [faqs, setFaqs] = useState([]);
  const [greetings, setGreetings] = useState({
    main: '',
    afterHours: '',
    busy: '',
    voicemail: ''
  });
  const [conversationFlows, setConversationFlows] = useState([]);
  const [businessInfo, setBusinessInfo] = useState({
    services: '',
    policies: '',
    pricing: '',
    hours: '',
    specialInstructions: ''
  });

  // Form states
  const [newFaq, setNewFaq] = useState({ question: '', answer: '' });
  const [showAddFaq, setShowAddFaq] = useState(false);
  const [editingFaq, setEditingFaq] = useState(null);

  useEffect(() => {
    fetchBusinesses();
  }, []);

  useEffect(() => {
    if (selectedBusiness) {
      loadTrainingData();
    }
  }, [selectedBusiness]);

  const fetchBusinesses = async () => {
    try {
      setLoading(true);
      const response = await businessAPI.getUserBusinesses();
      const businessList = response.data.data || [];
      setBusinesses(businessList);
      
      if (businessList.length > 0) {
        setSelectedBusiness(businessList[0]);
      }
    } catch (err) {
      console.error('Error fetching businesses:', err);
      toast.error('Failed to load businesses');
    } finally {
      setLoading(false);
    }
  };

  const loadTrainingData = async () => {
    try {
      console.log('📚 Loading REAL training data from backend prompt.py for business:', selectedBusiness.name);
      
      // First try to load existing training data for this business
      try {
        const existingResponse = await aiTrainingAPI.getTrainingData(selectedBusiness.id);
        if (existingResponse.data.success) {
          const existingData = existingResponse.data.data;
          console.log('✅ Loaded existing training data:', existingData);
          
          if (existingData.faqs) setFaqs(existingData.faqs);
          if (existingData.greetings) setGreetings(existingData.greetings);
          if (existingData.businessInfo) setBusinessInfo(existingData.businessInfo);
          if (existingData.conversationFlows) setConversationFlows(existingData.conversationFlows);
          
          return; // Exit early if we have existing data
        }
      } catch (existingError) {
        console.log('ℹ️ No existing training data found, loading defaults');
      }
      
      // Load default template from backend prompt.py
      try {
        const templateResponse = await aiTrainingAPI.getPromptTemplate(
          selectedBusiness.business_type || selectedBusiness.type || 'general',
          selectedBusiness.name,
          {
            phone: selectedBusiness.phone,
            email: selectedBusiness.email,
            address: selectedBusiness.address,
            hours: selectedBusiness.hours
          }
        );
        
        if (templateResponse.data.success) {
          const template = templateResponse.data.data;
          console.log('✅ Loaded backend prompt template:', template);
          
          // Set greetings from the backend template
          setGreetings({
            main: template.WELCOME_MESSAGE || `Thank you for calling ${selectedBusiness.name}! I'm Connecto from SME Analytica. How may I help you today?`,
            afterHours: `Thank you for calling ${selectedBusiness.name}. We're currently closed, but I can help schedule an appointment for you.`,
            busy: `Thank you for calling ${selectedBusiness.name}. All our representatives are currently busy, but I can assist you right away.`,
            voicemail: `Thank you for calling ${selectedBusiness.name}. Please leave your message after the tone, or I can help you schedule an appointment.`
          });
          
          // Extract business info from template instructions if available
          if (template.INSTRUCTIONS) {
            const instructions = template.INSTRUCTIONS;
            
            // Extract business hours from template
            const hoursMatch = instructions.match(/BUSINESS HOURS:\s*([^]*?)(?:\n\n|\nCONTACT)/);
            const hoursText = hoursMatch ? hoursMatch[1].trim() : '';
            
            // Extract contact info from template
            const contactMatch = instructions.match(/CONTACT INFORMATION:\s*([^]*?)(?:\n\n|\nABOUT SME)/);
            const contactText = contactMatch ? contactMatch[1].trim() : '';
            
            setBusinessInfo(prev => ({
              ...prev,
              hours: hoursText,
              services: `Professional ${selectedBusiness.business_type || 'business'} services`,
              policies: 'Please check our website for current policies and procedures.',
              pricing: 'Contact us for current pricing information.',
              specialInstructions: 'Powered by SME Analytica AI technology for enhanced customer service.'
            }));
          }
        }
      } catch (templateError) {
        console.log('⚠️ Could not load backend template, using defaults');
      }
      
      // Load default FAQs based on business type (matches backend prompt.py business types)
      const defaultFaqs = getDefaultFaqsForBusinessType(selectedBusiness.business_type || selectedBusiness.type);
      setFaqs(defaultFaqs);
      
      // Set default greetings if not loaded from template
      if (!greetings.main) {
        setGreetings({
          main: `Thank you for calling ${selectedBusiness.name}! I'm Connecto from SME Analytica. How may I help you today?`,
          afterHours: `Thank you for calling ${selectedBusiness.name}. We're currently closed, but I can help schedule an appointment for you.`,
          busy: `Thank you for calling ${selectedBusiness.name}. All our representatives are currently busy, but I can assist you right away.`,
          voicemail: `Thank you for calling ${selectedBusiness.name}. Please leave your message after the tone, or I can help you schedule an appointment.`
        });
      }

    } catch (error) {
      console.error('Error loading training data:', error);
      toast.error('Failed to load training data from backend');
      
      // Fallback to local defaults
      const defaultFaqs = getDefaultFaqsForBusinessType(selectedBusiness.business_type || selectedBusiness.type);
      setFaqs(defaultFaqs);
      
      setGreetings({
        main: `Thank you for calling ${selectedBusiness.name}! I'm Connecto from SME Analytica. How may I help you today?`,
        afterHours: `Thank you for calling ${selectedBusiness.name}. We're currently closed, but I can help schedule an appointment for you.`,
        busy: `Thank you for calling ${selectedBusiness.name}. All our representatives are currently busy, but I can assist you right away.`,
        voicemail: `Thank you for calling ${selectedBusiness.name}. Please leave your message after the tone, or I can help you schedule an appointment.`
      });
    }
  };

  const getDefaultFaqsForBusinessType = (businessType) => {
    // These match the comprehensive business templates from backend/prompt.py
    const faqTemplates = {
      auto_service: [
        { id: 1, question: 'What services do you offer?', answer: 'We provide full automotive repair, maintenance, oil changes, and diagnostics.' },
        { id: 2, question: 'Do you provide estimates?', answer: 'Yes, we provide free estimates for all repair work. I can schedule an inspection.' },
        { id: 3, question: 'What are your warranty terms?', answer: 'We offer a 12-month warranty on all parts and labor.' },
        { id: 4, question: 'Do you work on all car makes?', answer: 'Yes, we service all makes and models, both domestic and foreign vehicles.' },
        { id: 5, question: 'How long does an oil change take?', answer: 'Oil changes typically take 30-60 minutes depending on your vehicle.' },
        { id: 6, question: 'Do you have loaner cars available?', answer: 'Yes, we provide loaner cars for longer repairs when available.' }
      ],
      nail_salon: [
        { id: 1, question: 'What nail services do you offer?', answer: 'We offer manicures, pedicures, gel polish, acrylics, and custom nail art.' },
        { id: 2, question: 'How much does a basic manicure cost?', answer: 'Basic manicures start at €25 and take about 30 minutes.' },
        { id: 3, question: 'Do you offer gel manicures?', answer: 'Yes, gel manicures are €35 and last about 2 weeks longer than regular polish.' },
        { id: 4, question: 'Can I request a specific nail technician?', answer: 'Absolutely! Let me know your preferred technician when booking.' },
        { id: 5, question: 'Do you offer discounts for new clients?', answer: 'Yes, new clients receive a 10% discount on their first visit.' }
      ],
      restaurant: [
        { id: 1, question: 'What are your hours?', answer: 'We are open Monday-Thursday 11 AM-10 PM, Friday-Saturday 11 AM-11 PM, Sunday 10 AM-9 PM with brunch.' },
        { id: 2, question: 'Do you take reservations?', answer: 'Yes, we accept reservations. I can help you make one right now!' },
        { id: 3, question: 'What type of cuisine do you serve?', answer: 'We serve authentic Spanish cuisine with a modern twist.' },
        { id: 4, question: 'Do you have vegetarian options?', answer: 'Yes, we have several delicious vegetarian dishes on our menu.' },
        { id: 5, question: 'Do you offer takeout or delivery?', answer: 'Yes, we offer both takeout and delivery. I can take your order now.' },
        { id: 6, question: 'Can you accommodate large parties?', answer: 'Yes, for parties larger than 8, we have special seating arrangements available.' }
      ],
      barbershop: [
        { id: 1, question: 'What services do you offer?', answer: 'We offer haircuts, beard trims, hot towel shaves, and grooming services.' },
        { id: 2, question: 'How much does a haircut cost?', answer: 'Standard haircuts are €25-€35, depending on length and complexity.' },
        { id: 3, question: 'Do you take walk-ins?', answer: 'Yes, we accept walk-ins, though appointments are recommended to avoid waiting.' },
        { id: 4, question: 'Do you offer kids haircuts?', answer: 'Yes, kids haircuts (under 12) are €20 and take about 20 minutes.' },
        { id: 5, question: 'Can I request a specific barber?', answer: 'Absolutely! Let me know your preferred barber when scheduling.' }
      ],
      medical_office: [
        { id: 1, question: 'What insurance do you accept?', answer: 'We accept most major insurance plans. I can verify your coverage when scheduling.' },
        { id: 2, question: 'How do I schedule an appointment?', answer: 'I can help you schedule an appointment right now. What type of visit do you need?' },
        { id: 3, question: 'What should I bring to my appointment?', answer: 'Please bring your insurance card, ID, and any relevant medical records.' },
        { id: 4, question: 'Do you have same-day appointments?', answer: 'We have limited same-day availability for urgent matters. Let me check our schedule.' },
        { id: 5, question: 'What are your office hours?', answer: 'We are open Monday-Friday 8 AM-5 PM, Saturday 9 AM-12 PM for urgent care only.' }
      ],
      dental_office: [
        { id: 1, question: 'What dental services do you provide?', answer: 'We offer cleanings, exams, fillings, crowns, root canals, and cosmetic dentistry.' },
        { id: 2, question: 'How often should I have a cleaning?', answer: 'We recommend cleanings every 6 months for optimal oral health.' },
        { id: 3, question: 'Do you handle dental emergencies?', answer: 'Yes, we have emergency appointments available. What type of emergency are you experiencing?' },
        { id: 4, question: 'What insurance do you accept?', answer: 'We accept most dental insurance plans. I can verify your coverage when scheduling.' },
        { id: 5, question: 'How long does a typical cleaning take?', answer: 'Regular cleanings take about 60 minutes, comprehensive exams take 90 minutes.' }
      ],
      spa: [
        { id: 1, question: 'What spa services do you offer?', answer: 'We offer massages, facials, body wraps, manicures, pedicures, and wellness treatments.' },
        { id: 2, question: 'How much does a massage cost?', answer: 'Swedish massages are €85 for 60 minutes, €125 for 90 minutes.' },
        { id: 3, question: 'Should I arrive early for my appointment?', answer: 'Yes, please arrive 15 minutes early to complete paperwork and prepare for your service.' },
        { id: 4, question: 'Can I book treatments for couples?', answer: 'Absolutely! We offer couples massage rooms and spa packages.' },
        { id: 5, question: 'Do you offer package deals?', answer: 'Yes, we have various spa packages combining multiple treatments at discounted rates.' }
      ],
      law_firm: [
        { id: 1, question: 'What areas of law do you practice?', answer: 'We specialize in business law, family law, and estate planning.' },
        { id: 2, question: 'How much do consultations cost?', answer: 'Initial consultations are $200 for one hour. I can schedule this for you.' },
        { id: 3, question: 'How quickly can I get an appointment?', answer: 'We typically have availability within 3-5 business days for new clients.' },
        { id: 4, question: 'Do you offer payment plans?', answer: 'Yes, we offer flexible payment arrangements. Our attorney can discuss options during your consultation.' },
        { id: 5, question: 'Do you handle emergency legal matters?', answer: 'For clients with court dates within 48 hours or custody situations, we prioritize urgent scheduling.' }
      ],
      real_estate: [
        { id: 1, question: 'Are you currently buying or selling?', answer: 'I can help with both! Let me connect you with the right agent for your needs.' },
        { id: 2, question: 'Can you help me find properties in my price range?', answer: 'Absolutely! What is your budget and preferred location?' },
        { id: 3, question: 'Do you handle rental properties?', answer: 'Yes, we handle both sales and rentals. What type of property are you looking for?' },
        { id: 4, question: 'Can I schedule a property viewing?', answer: 'Yes, I can schedule a viewing with one of our agents. Which property interests you?' },
        { id: 5, question: 'Do you provide property management services?', answer: 'Yes, we offer full property management for landlords and emergency maintenance services.' }
      ],
      fitness_center: [
        { id: 1, question: 'What membership options do you have?', answer: 'We offer Basic (€39.99/month), Premium (€59.99/month), and Family (€89.99/month) memberships.' },
        { id: 2, question: 'Can I get a tour of the facility?', answer: 'Absolutely! I can schedule a tour with one of our fitness consultants.' },
        { id: 3, question: 'Do you offer personal training?', answer: 'Yes, we have certified personal trainers available. What are your fitness goals?' },
        { id: 4, question: 'What classes do you offer?', answer: 'We offer yoga, spinning, CrossFit, Zumba, and strength training classes.' },
        { id: 5, question: 'What are your hours?', answer: 'Monday-Thursday 5 AM-11 PM, Friday 5 AM-10 PM, weekends 7 AM-8 PM.' }
      ]
    };

    return faqTemplates[businessType] || [
      { id: 1, question: 'What are your business hours?', answer: 'Please check our website for current hours or I can help you schedule an appointment.' },
      { id: 2, question: 'How can I schedule an appointment?', answer: 'I can help you schedule an appointment right now. What date and time works best for you?' },
      { id: 3, question: 'What services do you offer?', answer: 'We offer a full range of professional services. I can provide more details about any specific service.' }
    ];
  };

  const saveTrainingData = async () => {
    try {
      setSaving(true);
      
      const trainingData = {
        business_id: selectedBusiness.id,
        faqs,
        greetings,
        conversationFlows,
        businessInfo
      };

      console.log('💾 Saving REAL training data to backend:', trainingData);
      
      // Real API call to save training data to backend
      const response = await aiTrainingAPI.updateTrainingData(selectedBusiness.id, trainingData);
      
      if (response.data.success) {
        toast.success('Training data saved successfully to backend!');
        console.log('✅ Training data saved to backend database');
      } else {
        throw new Error(response.data.message || 'Failed to save training data');
      }
      
    } catch (error) {
      console.error('Error saving training data:', error);
      toast.error('Failed to save training data');
    } finally {
      setSaving(false);
    }
  };

  const addFaq = () => {
    if (!newFaq.question.trim() || !newFaq.answer.trim()) {
      toast.error('Please fill in both question and answer');
      return;
    }

    const faq = {
      id: Date.now(),
      question: newFaq.question.trim(),
      answer: newFaq.answer.trim()
    };

    setFaqs(prev => [...prev, faq]);
    setNewFaq({ question: '', answer: '' });
    setShowAddFaq(false);
    toast.success('FAQ added successfully');
  };

  const updateFaq = (id, updatedFaq) => {
    setFaqs(prev => prev.map(faq => 
      faq.id === id ? { ...faq, ...updatedFaq } : faq
    ));
    setEditingFaq(null);
    toast.success('FAQ updated successfully');
  };

  const deleteFaq = (id) => {
    setFaqs(prev => prev.filter(faq => faq.id !== id));
    toast.success('FAQ deleted successfully');
  };

  const testAIResponse = async (question) => {
    try {
      toast.loading('Testing AI response with backend...', { id: 'test-ai' });
      
      // Real API call to test AI response using backend prompt.py system
      try {
        const response = await aiTrainingAPI.testAIResponse(selectedBusiness.id, question);
        
        if (response.data.success) {
          const aiResponse = response.data.data.response;
          toast.success('AI Response: ' + aiResponse, { 
            id: 'test-ai',
            duration: 8000
          });
          console.log('✅ Real AI response from backend:', aiResponse);
        } else {
          throw new Error(response.data.message || 'Failed to get AI response');
        }
      } catch (apiError) {
        console.log('⚠️ Backend AI testing not available, using local fallback');
        
        // Fallback to local FAQ matching when backend is not ready
        const mockResponse = faqs.find(faq => 
          faq.question.toLowerCase().includes(question.toLowerCase())
        )?.answer || "I'd be happy to help you with that. Let me connect you with someone who can provide more specific information.";
        
        toast.success('Local Response: ' + mockResponse, { 
          id: 'test-ai',
          duration: 8000
        });
      }
      
    } catch (error) {
      console.error('Error testing AI response:', error);
      toast.error('Failed to test AI response', { id: 'test-ai' });
    }
  };

  const sections = [
    { id: 'overview', label: 'Training Overview', icon: Brain },
    { id: 'greetings', label: 'Greetings & Prompts', icon: MessageSquare },
    { id: 'faqs', label: 'FAQs & Knowledge Base', icon: BookOpen },
    { id: 'conversations', label: 'Conversation Flows', icon: Users },
    { id: 'business-info', label: 'Business Information', icon: Building2 },
    { id: 'testing', label: 'AI Testing', icon: Play }
  ];

  if (loading) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold text-secondary-900">AI Training</h1>
          <p className="text-secondary-600">Train your AI voice assistant with business-specific knowledge.</p>
        </div>
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto"></div>
            <p className="text-secondary-600 mt-2">Loading training interface...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-secondary-900">AI Training</h1>
          <p className="text-secondary-600">
            Train your AI voice assistant with business-specific knowledge and conversation flows.
          </p>
        </div>
        
        <div className="flex items-center space-x-3">
          <select
            value={selectedBusiness?.id || ''}
            onChange={(e) => {
              const business = businesses.find(b => b.id === e.target.value);
              setSelectedBusiness(business);
            }}
            className="px-3 py-2 border border-secondary-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
          >
            {businesses.map((business) => (
              <option key={business.id} value={business.id}>
                {business.name}
              </option>
            ))}
          </select>
          
          <Button
            onClick={saveTrainingData}
            loading={saving}
            icon={Save}
          >
            Save Training
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Navigation Sidebar */}
        <div className="lg:col-span-1">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Brain className="w-5 h-5" />
                Training Sections
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              {sections.map((section) => (
                <button
                  key={section.id}
                  onClick={() => setActiveSection(section.id)}
                  className={`w-full flex items-center gap-3 p-3 rounded-lg text-left transition-colors ${
                    activeSection === section.id
                      ? 'bg-primary-100 text-primary-700 border border-primary-300'
                      : 'hover:bg-secondary-50 text-secondary-700'
                  }`}
                >
                  <section.icon className="w-4 h-4" />
                  <span className="text-sm font-medium">{section.label}</span>
                </button>
              ))}
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <div className="lg:col-span-3">
          {activeSection === 'overview' && (
            <Card>
              <CardHeader>
                <CardTitle>Training Overview</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
                  <h3 className="text-lg font-medium text-blue-900 mb-3">AI Training Status for {selectedBusiness?.name}</h3>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="flex items-center gap-3">
                      <CheckCircle className="w-5 h-5 text-green-600" />
                      <span className="text-sm">Basic greetings configured</span>
                    </div>
                    <div className="flex items-center gap-3">
                      <CheckCircle className="w-5 h-5 text-green-600" />
                      <span className="text-sm">{faqs.length} FAQs in knowledge base</span>
                    </div>
                    <div className="flex items-center gap-3">
                      <AlertCircle className="w-5 h-5 text-amber-600" />
                      <span className="text-sm">Conversation flows need setup</span>
                    </div>
                    <div className="flex items-center gap-3">
                      <AlertCircle className="w-5 h-5 text-amber-600" />
                      <span className="text-sm">Business info needs completion</span>
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Button
                    variant="outline"
                    className="h-20 flex-col justify-center"
                    onClick={() => setActiveSection('greetings')}
                  >
                    <MessageSquare className="w-6 h-6 mb-2 text-primary-600" />
                    <span className="text-sm">Configure Greetings</span>
                  </Button>
                  <Button
                    variant="outline"
                    className="h-20 flex-col justify-center"
                    onClick={() => setActiveSection('faqs')}
                  >
                    <BookOpen className="w-6 h-6 mb-2 text-primary-600" />
                    <span className="text-sm">Manage FAQs</span>
                  </Button>
                  <Button
                    variant="outline"
                    className="h-20 flex-col justify-center"
                    onClick={() => setActiveSection('testing')}
                  >
                    <Play className="w-6 h-6 mb-2 text-primary-600" />
                    <span className="text-sm">Test AI Responses</span>
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {activeSection === 'greetings' && (
            <Card>
              <CardHeader>
                <CardTitle>Greetings & Prompts</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-secondary-700 mb-2">
                      Main Greeting
                    </label>
                    <textarea
                      value={greetings.main}
                      onChange={(e) => setGreetings(prev => ({ ...prev, main: e.target.value }))}
                      className="w-full p-3 border border-secondary-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                      rows={3}
                      placeholder="Enter the main greeting message..."
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-secondary-700 mb-2">
                      After Hours Greeting
                    </label>
                    <textarea
                      value={greetings.afterHours}
                      onChange={(e) => setGreetings(prev => ({ ...prev, afterHours: e.target.value }))}
                      className="w-full p-3 border border-secondary-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                      rows={3}
                      placeholder="Enter the after hours greeting..."
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-secondary-700 mb-2">
                      Busy/Hold Greeting
                    </label>
                    <textarea
                      value={greetings.busy}
                      onChange={(e) => setGreetings(prev => ({ ...prev, busy: e.target.value }))}
                      className="w-full p-3 border border-secondary-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                      rows={3}
                      placeholder="Enter the busy/hold greeting..."
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-secondary-700 mb-2">
                      Voicemail Prompt
                    </label>
                    <textarea
                      value={greetings.voicemail}
                      onChange={(e) => setGreetings(prev => ({ ...prev, voicemail: e.target.value }))}
                      className="w-full p-3 border border-secondary-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                      rows={3}
                      placeholder="Enter the voicemail prompt..."
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {activeSection === 'faqs' && (
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle>FAQs & Knowledge Base</CardTitle>
                  <Button
                    onClick={() => setShowAddFaq(true)}
                    icon={Plus}
                    size="sm"
                  >
                    Add FAQ
                  </Button>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                {showAddFaq && (
                  <div className="p-4 border border-primary-200 bg-primary-50 rounded-lg space-y-4">
                    <h4 className="font-medium text-secondary-900">Add New FAQ</h4>
                    <div className="space-y-3">
                      <Input
                        label="Question"
                        value={newFaq.question}
                        onChange={(e) => setNewFaq(prev => ({ ...prev, question: e.target.value }))}
                        placeholder="e.g., What are your hours?"
                      />
                      <div>
                        <label className="block text-sm font-medium text-secondary-700 mb-2">
                          Answer
                        </label>
                        <textarea
                          value={newFaq.answer}
                          onChange={(e) => setNewFaq(prev => ({ ...prev, answer: e.target.value }))}
                          className="w-full p-3 border border-secondary-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                          rows={3}
                          placeholder="Enter the answer the AI should give..."
                        />
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Button onClick={addFaq} size="sm">
                        Add FAQ
                      </Button>
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => setShowAddFaq(false)}
                      >
                        Cancel
                      </Button>
                    </div>
                  </div>
                )}

                <div className="space-y-3">
                  {faqs.map((faq) => (
                    <div key={faq.id} className="p-4 border border-secondary-200 rounded-lg">
                      {editingFaq === faq.id ? (
                        <div className="space-y-3">
                          <Input
                            label="Question"
                            value={faq.question}
                            onChange={(e) => updateFaq(faq.id, { question: e.target.value })}
                          />
                          <div>
                            <label className="block text-sm font-medium text-secondary-700 mb-2">
                              Answer
                            </label>
                            <textarea
                              value={faq.answer}
                              onChange={(e) => updateFaq(faq.id, { answer: e.target.value })}
                              className="w-full p-3 border border-secondary-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                              rows={3}
                            />
                          </div>
                          <div className="flex items-center gap-2">
                            <Button size="sm" onClick={() => setEditingFaq(null)}>
                              Save
                            </Button>
                            <Button 
                              variant="outline" 
                              size="sm"
                              onClick={() => setEditingFaq(null)}
                            >
                              Cancel
                            </Button>
                          </div>
                        </div>
                      ) : (
                        <div>
                          <div className="flex items-start justify-between mb-2">
                            <h4 className="font-medium text-secondary-900">Q: {faq.question}</h4>
                            <div className="flex items-center gap-2">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => testAIResponse(faq.question)}
                                icon={Play}
                              >
                                Test
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => setEditingFaq(faq.id)}
                                icon={Edit}
                              />
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => deleteFaq(faq.id)}
                                icon={Trash2}
                                className="text-error-600 hover:text-error-700"
                              />
                            </div>
                          </div>
                          <p className="text-sm text-secondary-600">A: {faq.answer}</p>
                        </div>
                      )}
                    </div>
                  ))}
                </div>

                {faqs.length === 0 && (
                  <div className="text-center py-8">
                    <BookOpen className="w-12 h-12 text-secondary-400 mx-auto mb-4" />
                    <p className="text-secondary-600">No FAQs added yet</p>
                    <p className="text-sm text-secondary-500 mt-1">
                      Add frequently asked questions to help your AI assist customers better
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {activeSection === 'conversations' && (
            <Card>
              <CardHeader>
                <CardTitle>Conversation Flows</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center py-12">
                  <Users className="w-12 h-12 text-secondary-400 mx-auto mb-4" />
                  <p className="text-secondary-600 mb-4">Conversation Flow Builder</p>
                  <p className="text-sm text-secondary-500 mb-6">
                    Create conversation flows for appointment booking, inquiries, and call routing.
                  </p>
                  <Button icon={Plus}>
                    Create Conversation Flow
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {activeSection === 'business-info' && (
            <Card>
              <CardHeader>
                <CardTitle>Business Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-secondary-700 mb-2">
                    Services Offered
                  </label>
                  <textarea
                    value={businessInfo.services}
                    onChange={(e) => setBusinessInfo(prev => ({ ...prev, services: e.target.value }))}
                    className="w-full p-3 border border-secondary-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                    rows={4}
                    placeholder="Describe your services in detail..."
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-secondary-700 mb-2">
                    Policies & Procedures
                  </label>
                  <textarea
                    value={businessInfo.policies}
                    onChange={(e) => setBusinessInfo(prev => ({ ...prev, policies: e.target.value }))}
                    className="w-full p-3 border border-secondary-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                    rows={4}
                    placeholder="Cancellation policies, payment terms, etc..."
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-secondary-700 mb-2">
                    Pricing Information
                  </label>
                  <textarea
                    value={businessInfo.pricing}
                    onChange={(e) => setBusinessInfo(prev => ({ ...prev, pricing: e.target.value }))}
                    className="w-full p-3 border border-secondary-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                    rows={3}
                    placeholder="General pricing information the AI can share..."
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-secondary-700 mb-2">
                    Special Instructions
                  </label>
                  <textarea
                    value={businessInfo.specialInstructions}
                    onChange={(e) => setBusinessInfo(prev => ({ ...prev, specialInstructions: e.target.value }))}
                    className="w-full p-3 border border-secondary-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                    rows={3}
                    placeholder="Any special instructions for the AI..."
                  />
                </div>
              </CardContent>
            </Card>
          )}

          {activeSection === 'testing' && (
            <Card>
              <CardHeader>
                <CardTitle>AI Testing</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="bg-green-50 border border-green-200 rounded-lg p-6">
                  <h3 className="text-lg font-medium text-green-900 mb-3">Test Your AI Assistant</h3>
                  <p className="text-green-700 mb-4">
                    Test how your AI will respond to customer questions using your current training data.
                  </p>
                  
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-green-800 mb-2">
                        Test Question
                      </label>
                      <div className="flex gap-3">
                        <input
                          type="text"
                          placeholder="Ask a question to test the AI..."
                          className="flex-1 p-3 border border-green-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                          onKeyPress={(e) => {
                            if (e.key === 'Enter') {
                              testAIResponse(e.target.value);
                              e.target.value = '';
                            }
                          }}
                        />
                        <Button
                          onClick={(e) => {
                            const input = e.target.closest('div').querySelector('input');
                            testAIResponse(input.value);
                            input.value = '';
                          }}
                          icon={Play}
                        >
                          Test
                        </Button>
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                      <h4 className="text-sm font-medium text-green-800 col-span-full">Quick Test Questions:</h4>
                      {faqs.slice(0, 4).map((faq) => (
                        <Button
                          key={faq.id}
                          variant="outline"
                          size="sm"
                          onClick={() => testAIResponse(faq.question)}
                          className="text-left justify-start"
                        >
                          {faq.question}
                        </Button>
                      ))}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
};

export default AITraining; 