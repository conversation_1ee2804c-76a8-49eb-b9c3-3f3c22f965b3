import React from 'react';
import { HelpCircle, ExternalLink, MessageSquare, FileText, Video, Mail } from 'lucide-react';
import { Card, CardHeader, CardTitle, CardContent } from '../components/common/Card';
import Button from '../components/common/Button';

const Help = () => {
  const helpSections = [
    {
      title: 'Getting Started',
      icon: FileText,
      items: [
        'Setting up your first business',
        'Connecting your phone number',
        'Configuring AI voice settings',
        'Testing your voice assistant'
      ]
    },
    {
      title: 'Phone Integration',
      icon: HelpCircle,
      items: [
        'SIP configuration guide',
        'Phone routing options',
        'Call forwarding setup',
        'Troubleshooting connection issues'
      ]
    },
    {
      title: 'Calendar & Appointments',
      icon: FileText,
      items: [
        'Google Calendar integration',
        'Outlook Calendar setup',
        'Managing appointment types',
        'Setting business hours'
      ]
    },
    {
      title: 'AI Training',
      icon: Video,
      items: [
        'Customizing conversation flows',
        'Business-specific prompts',
        'Handling complex scenarios',
        'Voice quality optimization'
      ]
    }
  ];

  const contactOptions = [
    {
      title: 'Live Chat',
      description: 'Get instant help from our support team',
      icon: MessageSquare,
      action: 'Start Chat',
      available: true
    },
    {
      title: 'Email Support',
      description: 'Send us a detailed message',
      icon: Mail,
      action: 'Send Email',
      email: '<EMAIL>'
    },
    {
      title: 'Documentation',
      description: 'Browse our complete user guide',
      icon: FileText,
      action: 'View Docs',
      url: 'https://docs.smeanalytica.dev'
    }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-secondary-900">Help & Support</h1>
        <p className="text-secondary-600">
          Get help with setting up and using your AI voice receptionist
        </p>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {contactOptions.map((option, index) => {
          const Icon = option.icon;
          return (
            <Card key={index} className="hover:shadow-md transition-shadow">
              <CardContent className="p-6 text-center">
                <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Icon className="w-6 h-6 text-primary-600" />
                </div>
                <h3 className="font-semibold text-secondary-900 mb-2">{option.title}</h3>
                <p className="text-sm text-secondary-600 mb-4">{option.description}</p>
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="w-full"
                  onClick={() => {
                    if (option.email) {
                      window.location.href = `mailto:${option.email}`;
                    } else if (option.url) {
                      window.open(option.url, '_blank');
                    }
                  }}
                  icon={option.url ? ExternalLink : undefined}
                >
                  {option.action}
                </Button>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Help Topics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {helpSections.map((section, index) => {
          const Icon = section.icon;
          return (
            <Card key={index}>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Icon className="w-5 h-5" />
                  {section.title}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  {section.items.map((item, itemIndex) => (
                    <li key={itemIndex} className="flex items-center gap-2 text-sm text-secondary-700 hover:text-primary-600 cursor-pointer">
                      <span className="w-1.5 h-1.5 bg-primary-600 rounded-full"></span>
                      {item}
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* FAQ Section */}
      <Card>
        <CardHeader>
          <CardTitle>Frequently Asked Questions</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-4">
            <div className="border-b border-secondary-200 pb-4">
              <h4 className="font-medium text-secondary-900 mb-2">
                How do I connect my business phone number?
              </h4>
              <p className="text-sm text-secondary-700">
                Go to your business settings, click on "Phone Setup", and follow the SIP configuration guide. 
                You'll receive credentials to configure your phone system to route calls to Connecto AI.
              </p>
            </div>
            
            <div className="border-b border-secondary-200 pb-4">
              <h4 className="font-medium text-secondary-900 mb-2">
                Can the AI book appointments directly to my calendar?
              </h4>
              <p className="text-sm text-secondary-700">
                Yes! Connect your Google Calendar or Outlook in the Calendar Integration section. 
                The AI will automatically check availability and book appointments based on your configured services.
              </p>
            </div>
            
            <div className="border-b border-secondary-200 pb-4">
              <h4 className="font-medium text-secondary-900 mb-2">
                What business types are supported?
              </h4>
              <p className="text-sm text-secondary-700">
                Connecto supports law firms, medical offices, dental clinics, auto repair shops, restaurants, 
                spas, fitness centers, barbershops, nail salons, and real estate offices. Each has specialized conversation flows.
              </p>
            </div>
            
            <div>
              <h4 className="font-medium text-secondary-900 mb-2">
                How do I customize what the AI says?
              </h4>
              <p className="text-sm text-secondary-700">
                Each business type comes with pre-configured prompts. You can customize these in the AI Configuration 
                section of your business settings, including greetings, service information, and appointment handling.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Contact Footer */}
      <Card>
        <CardContent className="p-6 text-center">
          <h3 className="font-semibold text-secondary-900 mb-2">Still need help?</h3>
          <p className="text-secondary-600 mb-4">
            Our support team is here to help you get the most out of Connecto AI
          </p>
          <div className="flex items-center justify-center gap-4">
            <Button variant="outline" icon={Mail}>
              Email Support
            </Button>
            <Button icon={MessageSquare}>
              Start Live Chat
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default Help;