import React, { useState, useEffect, useCallback } from 'react';
import { Link } from 'react-router-dom';
import {
  Building2,
  Plus,
  Phone,
  Mail,
  MapPin,
  Clock,
  DollarSign,
  FileText,
  Users,
  Calendar,
  Save,
  Edit,
  CheckCircle,
  AlertCircle,
  Mic,
  MessageSquare,
  Settings
} from 'lucide-react';
import { Card, CardHeader, CardTitle, CardContent } from '../components/common/Card';
import Button from '../components/common/Button';
import Input from '../components/common/Input';
import { businessAPI } from '../utils/api';
import toast from 'react-hot-toast';

// Memoized AI Config Section Component
const AIConfigSection = React.memo(({
  businessData,
  editingSection,
  setEditingSection,
  updateAIConfig,
  saveBusinessData,
  saving
}) => (
  <Card>
    <CardHeader>
      <CardTitle className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Mic className="w-5 h-5" />
          <span>AI Assistant Configuration</span>
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={() => setEditingSection(editingSection === 'ai' ? null : 'ai')}
        >
          <Edit className="w-4 h-4" />
        </Button>
      </CardTitle>
    </CardHeader>
    <CardContent>
      {editingSection === 'ai' ? (
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-secondary-700 mb-2">
              Custom Greeting Message
            </label>
            <textarea
              className="w-full p-3 border border-secondary-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              rows={3}
              value={businessData.ai_config.greeting_message}
              onChange={(e) => updateAIConfig('greeting_message', e.target.value)}
              placeholder="Thank you for calling [Business Name]. This is Connecto, your AI assistant. How may I help you today?"
            />
            <p className="text-xs text-secondary-500 mt-1">
              Leave blank to use the default greeting. Use [Business Name] as a placeholder.
            </p>
          </div>

          <div>
            <label className="block text-sm font-medium text-secondary-700 mb-2">
              Voice Tone
            </label>
            <select
              className="w-full p-3 border border-secondary-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              value={businessData.ai_config.voice_tone}
              onChange={(e) => updateAIConfig('voice_tone', e.target.value)}
            >
              <option value="professional">Professional</option>
              <option value="friendly">Friendly</option>
              <option value="casual">Casual</option>
            </select>
          </div>

          <div className="space-y-3">
            <label className="block text-sm font-medium text-secondary-700">
              AI Capabilities
            </label>

            <label className="flex items-center space-x-3">
              <input
                type="checkbox"
                checked={businessData.ai_config.can_book_appointments}
                onChange={(e) => updateAIConfig('can_book_appointments', e.target.checked)}
                className="rounded"
              />
              <span className="text-sm">Can book appointments</span>
            </label>

            <label className="flex items-center space-x-3">
              <input
                type="checkbox"
                checked={businessData.ai_config.can_provide_pricing}
                onChange={(e) => updateAIConfig('can_provide_pricing', e.target.checked)}
                className="rounded"
              />
              <span className="text-sm">Can provide pricing information</span>
            </label>

            <label className="flex items-center space-x-3">
              <input
                type="checkbox"
                checked={businessData.ai_config.can_provide_hours}
                onChange={(e) => updateAIConfig('can_provide_hours', e.target.checked)}
                className="rounded"
              />
              <span className="text-sm">Can provide business hours</span>
            </label>
          </div>

          <div>
            <label className="block text-sm font-medium text-secondary-700 mb-2">
              When to Transfer to Human
            </label>
            <textarea
              className="w-full p-3 border border-secondary-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              rows={3}
              value={businessData.ai_config.transfer_conditions}
              onChange={(e) => updateAIConfig('transfer_conditions', e.target.value)}
              placeholder="e.g., For complex technical issues, billing disputes, or when customer specifically requests to speak with a human..."
            />
          </div>

          <div className="flex space-x-3 pt-4">
            <Button onClick={saveBusinessData} disabled={saving}>
              <Save className="w-4 h-4 mr-2" />
              {saving ? 'Saving...' : 'Save AI Config'}
            </Button>
            <Button variant="outline" onClick={() => setEditingSection(null)}>
              Cancel
            </Button>
          </div>
        </div>
      ) : (
        <div className="space-y-3 text-sm">
          <div><strong>Voice Tone:</strong> {businessData.ai_config.voice_tone || 'professional'}</div>
          <div><strong>Can book appointments:</strong> {businessData.ai_config.can_book_appointments ? 'Yes' : 'No'}</div>
          <div><strong>Can provide pricing:</strong> {businessData.ai_config.can_provide_pricing ? 'Yes' : 'No'}</div>
          <div><strong>Can provide hours:</strong> {businessData.ai_config.can_provide_hours ? 'Yes' : 'No'}</div>
          {businessData.ai_config.greeting_message && (
            <div><strong>Custom greeting:</strong> {businessData.ai_config.greeting_message}</div>
          )}
        </div>
      )}
    </CardContent>
  </Card>
));

AIConfigSection.displayName = 'AIConfigSection';

// Memoized Basic Info Section Component
const BasicInfoSection = React.memo(({
  businessData,
  editingSection,
  setEditingSection,
  updateBusinessData,
  saveBusinessData,
  saving
}) => (
  <Card>
    <CardHeader>
      <CardTitle className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Building2 className="w-5 h-5" />
          <span>Basic Information</span>
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={() => setEditingSection(editingSection === 'basic' ? null : 'basic')}
        >
          <Edit className="w-4 h-4" />
        </Button>
      </CardTitle>
    </CardHeader>
    <CardContent>
      {editingSection === 'basic' ? (
        <div className="space-y-4">
          <Input
            label="Business Name"
            value={businessData.name}
            onChange={(e) => updateBusinessData('name', e.target.value)}
            placeholder="Enter your business name"
            required
          />

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Input
              label="Phone Number"
              value={businessData.phone}
              onChange={(e) => updateBusinessData('phone', e.target.value)}
              placeholder="+****************"
            />

            <Input
              label="Email Address"
              type="email"
              value={businessData.email}
              onChange={(e) => updateBusinessData('email', e.target.value)}
              placeholder="<EMAIL>"
            />
          </div>

          <Input
            label="Address"
            value={businessData.address}
            onChange={(e) => updateBusinessData('address', e.target.value)}
            placeholder="123 Main Street"
          />

          <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
            <Input
              label="City"
              value={businessData.city}
              onChange={(e) => updateBusinessData('city', e.target.value)}
              placeholder="Valencia"
            />

            <Input
              label="State/Province"
              value={businessData.state}
              onChange={(e) => updateBusinessData('state', e.target.value)}
              placeholder="Valencia"
            />

            <Input
              label="ZIP Code"
              value={businessData.postal_code}
              onChange={(e) => updateBusinessData('postal_code', e.target.value)}
              placeholder="46001"
            />
          </div>

          <Input
            label="Website"
            value={businessData.website}
            onChange={(e) => updateBusinessData('website', e.target.value)}
            placeholder="https://www.yourbusiness.com"
          />

          <div>
            <label className="block text-sm font-medium text-secondary-700 mb-2">
              Business Description
            </label>
            <textarea
              className="w-full p-3 border border-secondary-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              rows={4}
              value={businessData.description}
              onChange={(e) => updateBusinessData('description', e.target.value)}
              placeholder="Describe what your business does, your specialties, and what makes you unique..."
            />
          </div>

          <div className="flex space-x-3">
            <Button onClick={saveBusinessData} disabled={saving}>
              <Save className="w-4 h-4 mr-2" />
              {saving ? 'Saving...' : 'Save Changes'}
            </Button>
            <Button variant="outline" onClick={() => setEditingSection(null)}>
              Cancel
            </Button>
          </div>
        </div>
      ) : (
        <div className="space-y-3 text-sm">
          <div><strong>Name:</strong> {businessData.name || 'Not set'}</div>
          <div><strong>Phone:</strong> {businessData.phone || 'Not set'}</div>
          <div><strong>Email:</strong> {businessData.email || 'Not set'}</div>
          <div><strong>Address:</strong> {businessData.address ? `${businessData.address}, ${businessData.city}, ${businessData.state} ${businessData.postal_code}` : 'Not set'}</div>
          <div><strong>Website:</strong> {businessData.website || 'Not set'}</div>
          {businessData.description && (
            <div><strong>Description:</strong> {businessData.description}</div>
          )}
        </div>
      )}
    </CardContent>
  </Card>
));

BasicInfoSection.displayName = 'BasicInfoSection';

const Businesses = () => {
  const [businesses, setBusinesses] = useState([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState(null);
  const [selectedBusiness, setSelectedBusiness] = useState(null);
  const [editingSection, setEditingSection] = useState(null);

  const [businessData, setBusinessData] = useState({
    // Basic Information
    name: '',
    description: '',
    address: '',
    city: '',
    state: '',
    postal_code: '',
    phone: '',
    email: '',
    website: '',

    // Operating Hours
    hours: {
      monday: { open: '09:00', close: '17:00', closed: false },
      tuesday: { open: '09:00', close: '17:00', closed: false },
      wednesday: { open: '09:00', close: '17:00', closed: false },
      thursday: { open: '09:00', close: '17:00', closed: false },
      friday: { open: '09:00', close: '17:00', closed: false },
      saturday: { open: '10:00', close: '15:00', closed: false },
      sunday: { open: '10:00', close: '15:00', closed: true }
    },

    // Services & Pricing
    services: [
      { name: '', description: '', price: '', duration: '' }
    ],

    // Business Policies
    policies: {
      appointment_policy: '',
      cancellation_policy: '',
      payment_methods: '',
      special_instructions: ''
    },

    // AI Configuration
    ai_config: {
      greeting_message: '',
      voice_tone: 'professional', // professional, friendly, casual
      can_book_appointments: true,
      can_provide_pricing: true,
      can_provide_hours: true,
      transfer_conditions: ''
    }
  });

  useEffect(() => {
    fetchBusinesses();
  }, []);

  useEffect(() => {
    // Only load business data if we're not editing
    if (selectedBusiness?.id && !editingSection) {
      // Add a small delay to prevent interference with user input
      const timeoutId = setTimeout(() => {
        // Double-check we're still not editing after the delay
        if (!editingSection) {
          console.log('🔄 Loading business data for:', selectedBusiness.id);
          loadBusinessData(selectedBusiness.id);
        } else {
          console.log('🚫 Cancelled business data load - user started editing');
        }
      }, 100);

      // Cleanup timeout if component unmounts or dependencies change
      return () => clearTimeout(timeoutId);
    }
  }, [selectedBusiness?.id, editingSection]);

  const fetchBusinesses = async () => {
    try {
      setLoading(true);
      setError(null);

      console.log('📊 Fetching user businesses...');
      const response = await businessAPI.getUserBusinesses();

      console.log('✅ Businesses loaded:', response.data);
      const businessList = response.data.data || [];
      setBusinesses(businessList);

      // Auto-select first business if none selected
      if (businessList.length > 0 && !selectedBusiness) {
        setSelectedBusiness(businessList[0]);
        console.log('🎯 Auto-selected first business:', businessList[0].name);
      }
    } catch (err) {
      console.error('❌ Error fetching businesses:', err);
      setError(err.userMessage || err.message || 'Failed to load businesses');
    } finally {
      setLoading(false);
    }
  };

  const loadBusinessData = async (businessId) => {
    // Extra protection: don't load if actively editing
    if (editingSection) {
      console.log('🚫 Skipping data load - user is editing');
      return;
    }

    try {
      console.log('📋 Loading business data for:', businessId);
      const response = await businessAPI.getById(businessId);

      if (response.data.success) {
        const business = response.data.data;

        // Check again if user started editing during the API call
        if (!editingSection) {
          // Merge business data with form structure, ensuring no null/undefined values
          setBusinessData(prevData => ({
            ...prevData,
            // Basic Information - ensure no null values
            name: business.name || '',
            description: business.description || '',
            address: business.address || '',
            city: business.city || '',
            state: business.state || '',
            postal_code: business.postal_code || '',
            phone: business.phone || '',
            email: business.email || '',
            website: business.website || '',

            // Complex fields from settings - use defaults if not present
            hours: business.hours && typeof business.hours === 'object' ? business.hours : prevData.hours,
            services: Array.isArray(business.services) && business.services.length > 0 ? business.services : prevData.services,
            policies: business.policies && typeof business.policies === 'object' ? business.policies : prevData.policies,

            // AI Configuration - ensure all fields have defaults
            ai_config: {
              greeting_message: business.ai_config?.greeting_message || '',
              voice_tone: business.ai_config?.voice_tone || 'professional',
              can_book_appointments: business.ai_config?.can_book_appointments ?? true,
              can_provide_pricing: business.ai_config?.can_provide_pricing ?? true,
              can_provide_hours: business.ai_config?.can_provide_hours ?? true,
              transfer_conditions: business.ai_config?.transfer_conditions || ''
            }
          }));

          console.log('✅ Business data loaded');
        } else {
          console.log('🚫 Skipping data update - user started editing during API call');
        }
      }
    } catch (err) {
      console.error('❌ Error loading business data:', err);
      if (!editingSection) {
        toast.error('Failed to load business details');
      }
    }
  };

  const updateBusinessData = useCallback((field, value) => {
    setBusinessData(prev => ({
      ...prev,
      [field]: value
    }));
  }, []);

  const updateAIConfig = useCallback((field, value) => {
    setBusinessData(prev => ({
      ...prev,
      ai_config: {
        ...prev.ai_config,
        [field]: value
      }
    }));
  }, []);

  const saveBusinessData = async () => {
    try {
      setSaving(true);

      console.log('💾 Saving business data:', businessData);

      const response = await businessAPI.update(selectedBusiness.id, businessData);

      if (response.data.success) {
        toast.success('Business information saved successfully!');
        setEditingSection(null);

        // Update the local state immediately with the saved data
        const updatedBusiness = response.data.data;

        // Update the businesses list with the updated business
        setBusinesses(prevBusinesses =>
          prevBusinesses.map(business =>
            business.id === selectedBusiness.id
              ? { ...business, ...updatedBusiness }
              : business
          )
        );

        // Update the selected business
        setSelectedBusiness(prev => ({ ...prev, ...updatedBusiness }));

        // Update the business data state with the response data
        setBusinessData(prev => ({
          ...prev,
          ...updatedBusiness,
          // Ensure AI config is properly merged
          ai_config: {
            ...prev.ai_config,
            ...(updatedBusiness.ai_config || {})
          }
        }));

        console.log('✅ Business data saved and state updated');
      } else {
        throw new Error(response.data.message || 'Failed to save business data');
      }

    } catch (err) {
      console.error('❌ Error saving business data:', err);
      toast.error(err.userMessage || err.message || 'Failed to save business information');
    } finally {
      setSaving(false);
    }
  };

  const updateService = useCallback((index, field, value) => {
    setBusinessData(prev => {
      const updatedServices = [...prev.services];
      updatedServices[index] = {
        ...updatedServices[index],
        [field]: value
      };
      return {
        ...prev,
        services: updatedServices
      };
    });
  }, []);

  const removeService = useCallback((index) => {
    setBusinessData(prev => ({
      ...prev,
      services: prev.services.filter((_, i) => i !== index)
    }));
  }, []);

  const addService = useCallback(() => {
    setBusinessData(prev => ({
      ...prev,
      services: [...prev.services, { name: '', description: '', price: '', duration: '' }]
    }));
  }, []);

  const updateHours = useCallback((day, field, value) => {
    setBusinessData(prev => ({
      ...prev,
      hours: {
        ...prev.hours,
        [day]: {
          ...prev.hours[day],
          [field]: value
        }
      }
    }));
  }, []);

  const handleBusinessSelection = useCallback((business) => {
    // Clear editing state when switching businesses
    setEditingSection(null);
    setSelectedBusiness(business);
  }, []);

  const BusinessSelector = () => (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Building2 className="w-5 h-5" />
          <span>Select Business</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {businesses.map((business) => (
            <div
              key={business.id}
              onClick={() => handleBusinessSelection(business)}
              className={`p-3 rounded-lg border-2 cursor-pointer transition-all ${
                selectedBusiness?.id === business.id
                  ? 'border-primary-500 bg-primary-50'
                  : 'border-secondary-200 hover:border-secondary-300'
              }`}
            >
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-medium text-secondary-900">{business.name}</h3>
                  <p className="text-sm text-secondary-500">
                    {business.city ? `${business.city}, ${business.state}` : 'Location not set'}
                  </p>
                </div>
                {selectedBusiness?.id === business.id && (
                  <CheckCircle className="w-5 h-5 text-primary-600" />
                )}
              </div>
            </div>
          ))}

          <Link to="/businesses/new">
            <div className="p-3 rounded-lg border-2 border-dashed border-secondary-300 hover:border-primary-400 cursor-pointer transition-all">
              <div className="flex items-center space-x-3">
                <Plus className="w-5 h-5 text-secondary-600" />
                <span className="text-secondary-700">Add New Business</span>
              </div>
            </div>
          </Link>
        </div>
      </CardContent>
    </Card>
  );

  const HoursSection = () => (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Clock className="w-5 h-5" />
            <span>Operating Hours</span>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setEditingSection(editingSection === 'hours' ? null : 'hours')}
          >
            <Edit className="w-4 h-4" />
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent>
        {editingSection === 'hours' ? (
          <div className="space-y-4">
            {Object.entries(businessData.hours).map(([day, hours]) => (
              <div key={day} className="flex items-center space-x-4">
                <div className="w-20 text-sm font-medium capitalize">{day}</div>

                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={!hours.closed}
                    onChange={(e) => updateHours(day, 'closed', !e.target.checked)}
                    className="rounded"
                  />
                  <span className="text-sm">Open</span>
                </label>

                {!hours.closed && (
                  <>
                    <input
                      type="time"
                      value={hours.open}
                      onChange={(e) => updateHours(day, 'open', e.target.value)}
                      className="px-3 py-1 border border-secondary-300 rounded text-sm"
                    />
                    <span className="text-sm">to</span>
                    <input
                      type="time"
                      value={hours.close}
                      onChange={(e) => updateHours(day, 'close', e.target.value)}
                      className="px-3 py-1 border border-secondary-300 rounded text-sm"
                    />
                  </>
                )}

                {hours.closed && (
                  <span className="text-sm text-secondary-500">Closed</span>
                )}
              </div>
            ))}

            <div className="flex space-x-3 pt-4">
              <Button onClick={saveBusinessData} disabled={saving}>
                <Save className="w-4 h-4 mr-2" />
                {saving ? 'Saving...' : 'Save Hours'}
              </Button>
              <Button variant="outline" onClick={() => setEditingSection(null)}>
                Cancel
              </Button>
            </div>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
            {Object.entries(businessData.hours).map(([day, hours]) => (
              <div key={day} className="flex justify-between">
                <span className="font-medium capitalize">{day}:</span>
                <span>
                  {hours.closed ? 'Closed' : `${hours.open} - ${hours.close}`}
                </span>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );

  const ServicesSection = () => (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <DollarSign className="w-5 h-5" />
            <span>Services & Pricing</span>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setEditingSection(editingSection === 'services' ? null : 'services')}
          >
            <Edit className="w-4 h-4" />
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent>
        {editingSection === 'services' ? (
          <div className="space-y-4">
            {businessData.services.map((service, index) => (
              <div key={index} className="p-4 border border-secondary-200 rounded-lg">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-3">
                  <Input
                    label="Service Name"
                    value={service.name}
                    onChange={(e) => updateService(index, 'name', e.target.value)}
                    placeholder="e.g., Haircut, Oil Change, Consultation"
                  />

                  <Input
                    label="Price"
                    value={service.price}
                    onChange={(e) => updateService(index, 'price', e.target.value)}
                    placeholder="e.g., €25, €50-€100, Call for quote"
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-3">
                  <Input
                    label="Duration"
                    value={service.duration}
                    onChange={(e) => updateService(index, 'duration', e.target.value)}
                    placeholder="e.g., 30 minutes, 1-2 hours"
                  />

                  {businessData.services.length > 1 && (
                    <div className="flex items-end">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => removeService(index)}
                        className="text-red-600 hover:text-red-700"
                      >
                        Remove Service
                      </Button>
                    </div>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-secondary-700 mb-2">
                    Description
                  </label>
                  <textarea
                    className="w-full p-2 border border-secondary-300 rounded-lg text-sm"
                    rows={2}
                    value={service.description}
                    onChange={(e) => updateService(index, 'description', e.target.value)}
                    placeholder="Brief description of this service..."
                  />
                </div>
              </div>
            ))}

            <Button variant="outline" onClick={addService}>
              <Plus className="w-4 h-4 mr-2" />
              Add Service
            </Button>

            <div className="flex space-x-3 pt-4">
              <Button onClick={saveBusinessData} disabled={saving}>
                <Save className="w-4 h-4 mr-2" />
                {saving ? 'Saving...' : 'Save Services'}
              </Button>
              <Button variant="outline" onClick={() => setEditingSection(null)}>
                Cancel
              </Button>
            </div>
          </div>
        ) : (
          <div className="space-y-3">
            {businessData.services.filter(s => s.name).map((service, index) => (
              <div key={index} className="p-3 bg-secondary-50 rounded-lg">
                <div className="flex justify-between items-start">
                  <div>
                    <h4 className="font-medium">{service.name}</h4>
                    {service.description && (
                      <p className="text-sm text-secondary-600 mt-1">{service.description}</p>
                    )}
                  </div>
                  <div className="text-right">
                    {service.price && <div className="font-medium text-primary-600">{service.price}</div>}
                    {service.duration && <div className="text-sm text-secondary-500">{service.duration}</div>}
                  </div>
                </div>
              </div>
            ))}

            {!businessData.services.some(s => s.name) && (
              <p className="text-secondary-500 text-sm">No services added yet</p>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );

  if (loading) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold text-secondary-900">Business Information</h1>
          <p className="text-secondary-600">Configure your business details for the AI voice assistant.</p>
        </div>
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto"></div>
            <p className="text-secondary-600 mt-2">Loading businesses...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-secondary-900">Business Information</h1>
          <p className="text-secondary-600">
            Configure your business details for the AI voice assistant.
          </p>
        </div>

        <div className="flex items-center space-x-3">
          <Link to="/ai-training">
            <Button variant="outline" className="flex items-center space-x-2">
              <MessageSquare className="w-4 h-4" />
              <span>AI Training</span>
            </Button>
          </Link>

          <Link to="/preview">
            <Button variant="outline" className="flex items-center space-x-2">
              <Mic className="w-4 h-4" />
              <span>Test Voice</span>
            </Button>
          </Link>

          <Link to="/businesses/new">
            <Button className="flex items-center space-x-2">
              <Plus className="w-4 h-4" />
              <span>Add Business</span>
            </Button>
          </Link>
        </div>
      </div>

      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <AlertCircle className="w-5 h-5 text-red-600" />
              <p className="text-red-700 font-medium">Error loading businesses</p>
            </div>
            <p className="text-red-600 text-sm mt-1">{error}</p>
          </CardContent>
        </Card>
      )}

      {!error && businesses.length === 0 && !loading && (
        <Card>
          <CardContent className="text-center py-12">
            <Building2 className="w-12 h-12 text-secondary-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-secondary-900 mb-2">No businesses found</h3>
            <p className="text-secondary-600 mb-6">
              Add your first business to start configuring your AI voice assistant.
            </p>
            <Link to="/businesses/new">
              <Button className="flex items-center space-x-2">
                <Plus className="w-4 h-4" />
                <span>Add Your First Business</span>
              </Button>
            </Link>
          </CardContent>
        </Card>
      )}

      {!error && businesses.length > 0 && (
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Business Selector - Left Column */}
          <div className="lg:col-span-1">
            <BusinessSelector />
          </div>

          {/* Business Information Forms - Right Columns */}
          <div className="lg:col-span-3 space-y-6">
            {selectedBusiness ? (
              <>
                <BasicInfoSection
                  businessData={businessData}
                  editingSection={editingSection}
                  setEditingSection={setEditingSection}
                  updateBusinessData={updateBusinessData}
                  saveBusinessData={saveBusinessData}
                  saving={saving}
                />
                <HoursSection />
                <ServicesSection />
                <AIConfigSection
                  businessData={businessData}
                  editingSection={editingSection}
                  setEditingSection={setEditingSection}
                  updateAIConfig={updateAIConfig}
                  saveBusinessData={saveBusinessData}
                  saving={saving}
                />

                {/* Quick Actions */}
                <Card className="border-primary-200 bg-primary-50">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="text-lg font-semibold text-secondary-900 mb-2">
                          Ready to Test Your AI Assistant?
                        </h3>
                        <p className="text-sm text-secondary-600 mb-4">
                          Test how your AI voice assistant will sound with your business information.
                        </p>
                      </div>
                      <div className="flex space-x-3">
                        <Link to="/ai-training">
                          <Button variant="outline" className="flex items-center space-x-2">
                            <MessageSquare className="w-4 h-4" />
                            <span>Train AI</span>
                          </Button>
                        </Link>
                        <Link to={`/preview?business=${selectedBusiness.id}`}>
                          <Button className="flex items-center space-x-2">
                            <Mic className="w-4 h-4" />
                            <span>Test Voice</span>
                          </Button>
                        </Link>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </>
            ) : (
              <Card>
                <CardContent className="text-center py-12">
                  <Building2 className="w-12 h-12 text-secondary-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-secondary-900 mb-2">Select a business</h3>
                  <p className="text-secondary-600">
                    Choose a business from the left to configure its information.
                  </p>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default Businesses;
