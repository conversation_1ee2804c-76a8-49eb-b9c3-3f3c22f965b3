import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { 
  Building2, 
  Phone, 
  Calendar, 
  Clock, 
  Settings, 
  BarChart3, 
  Headphones,
  ChevronRight,
  AlertCircle,
  CheckCircle
} from 'lucide-react';
import { Card, CardHeader, CardTitle, CardContent } from '../components/common/Card';
import Button from '../components/common/Button';
import LoadingSpinner from '../components/common/LoadingSpinner';
import PhoneIntegration from '../components/business/PhoneIntegration';
import CalendarIntegration from '../components/business/CalendarIntegration';
import BusinessHours from '../components/business/BusinessHours';
import { businessAPI } from '../utils/api';
import toast from 'react-hot-toast';

const BusinessDetail = () => {
  const { id } = useParams();
  const [business, setBusiness] = useState(null);
  const [activeTab, setActiveTab] = useState('overview');
  const [loading, setLoading] = useState(true);
  const [setupStatus, setSetupStatus] = useState({
    phone_connected: false,
    calendar_connected: false,
    hours_configured: false,
    ai_trained: false
  });

  const tabs = [
    { id: 'overview', label: 'Overview', icon: Building2 },
    { id: 'phone', label: 'Phone Setup', icon: Phone },
    { id: 'calendar', label: 'Calendar & Appointments', icon: Calendar },
    { id: 'hours', label: 'Business Hours', icon: Clock },
    { id: 'ai-config', label: 'AI Configuration', icon: Settings },
    { id: 'analytics', label: 'Analytics', icon: BarChart3 }
  ];

  useEffect(() => {
    fetchBusinessDetails();
  }, [id]);

  const fetchBusinessDetails = async () => {
    try {
      setLoading(true);
      
      console.log('🏢 Fetching REAL business details for ID:', id);
      
      // Handle "new" business creation case
      if (id === 'new') {
        console.log('✨ New business creation mode');
        setBusiness({
          id: 'new',
          name: '',
          description: '',
          type: 'restaurant',
          address: '',
          phone: '',
          email: '',
          website: '',
          is_active: true
        });
        setSetupStatus({
          phone_connected: false,
          calendar_connected: false,
          hours_configured: false,
          ai_trained: false
        });
        return;
      }
      
      // Real API call to get business details
      const businessResponse = await businessAPI.getById(id);
      
      if (businessResponse.data.success) {
        const businessData = businessResponse.data.data;
        console.log('✅ Real business data loaded:', businessData);
        setBusiness(businessData);
        
        // Fetch real setup status from business configuration
        const setupResponse = await businessAPI.getSetupStatus(id);
        if (setupResponse.data.success) {
          setSetupStatus(setupResponse.data.data);
        } else {
          // Default setup status if API not available yet
          const status = {
            phone_connected: !!businessData.phone,
            calendar_connected: false, // Will be determined by calendar integration check
            hours_configured: !!businessData.business_hours,
            ai_trained: !!businessData.ai_configuration
          };
          setSetupStatus(status);
        }
      } else {
        console.error('❌ Failed to fetch business details:', businessResponse.data.message);
        setBusiness(null);
      }
      
    } catch (error) {
      console.error('❌ Error fetching business details:', error);
      
      // If API call fails, check if business exists in our current business list
      try {
        const userBusinesses = await businessAPI.getUserBusinesses();
        if (userBusinesses.data.success) {
          const businesses = userBusinesses.data.data || [];
          const foundBusiness = businesses.find(b => b.id === id);
          
          if (foundBusiness) {
            console.log('✅ Found business in user list:', foundBusiness);
            setBusiness(foundBusiness);
            
            // Set default setup status based on available data
            const status = {
              phone_connected: !!foundBusiness.phone,
              calendar_connected: false,
              hours_configured: !!foundBusiness.business_hours,
              ai_trained: !!foundBusiness.ai_configuration
            };
            setSetupStatus(status);
          } else {
            console.warn('⚠️ Business not found in user businesses');
            setBusiness(null);
          }
        } else {
          setBusiness(null);
        }
      } catch (fallbackError) {
        console.error('❌ Fallback business fetch also failed:', fallbackError);
        setBusiness(null);
        toast.error('Failed to load business details');
      }
    } finally {
      setLoading(false);
    }
  };

  const handleIntegrationUpdate = (type) => {
    setSetupStatus(prev => ({
      ...prev,
      [`${type}_connected`]: true
    }));
    toast.success(`${type} integration updated successfully`);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" text="Loading business details..." />
      </div>
    );
  }

  if (!business) {
    return (
      <div className="text-center py-12">
        <AlertCircle className="w-12 h-12 text-error-500 mx-auto mb-4" />
        <h2 className="text-xl font-semibold text-secondary-900 mb-2">Business Not Found</h2>
        <p className="text-secondary-600">The business you're looking for doesn't exist or you don't have access to it.</p>
      </div>
    );
  }

  const setupComplete = Object.values(setupStatus).every(status => status);
  const completedSteps = Object.values(setupStatus).filter(status => status).length;
  const totalSteps = Object.keys(setupStatus).length;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-start justify-between">
        <div>
          <h1 className="text-2xl font-bold text-secondary-900">{business.name}</h1>
          <p className="text-secondary-600 capitalize">
            {business.type.replace('_', ' ')} • {business.address}
          </p>
        </div>
        <div className="flex items-center gap-3">
          <div className={`px-3 py-1 rounded-full text-sm font-medium ${
            business.is_active 
              ? 'bg-success-100 text-success-800' 
              : 'bg-error-100 text-error-800'
          }`}>
            {business.is_active ? 'Active' : 'Inactive'}
          </div>
          <Button icon={Settings}>
            Settings
          </Button>
        </div>
      </div>

      {/* Setup Progress */}
      {!setupComplete && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertCircle className="w-5 h-5 text-warning-600" />
              Setup Progress ({completedSteps}/{totalSteps})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="w-full bg-secondary-200 rounded-full h-2">
                <div 
                  className="bg-primary-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${(completedSteps / totalSteps) * 100}%` }}
                ></div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                <div className={`flex items-center gap-2 ${setupStatus.phone_connected ? 'text-success-600' : 'text-secondary-600'}`}>
                  {setupStatus.phone_connected ? <CheckCircle className="w-4 h-4" /> : <AlertCircle className="w-4 h-4" />}
                  <span className="text-sm">Phone integration</span>
                </div>
                <div className={`flex items-center gap-2 ${setupStatus.calendar_connected ? 'text-success-600' : 'text-secondary-600'}`}>
                  {setupStatus.calendar_connected ? <CheckCircle className="w-4 h-4" /> : <AlertCircle className="w-4 h-4" />}
                  <span className="text-sm">Calendar integration</span>
                </div>
                <div className={`flex items-center gap-2 ${setupStatus.hours_configured ? 'text-success-600' : 'text-secondary-600'}`}>
                  {setupStatus.hours_configured ? <CheckCircle className="w-4 h-4" /> : <AlertCircle className="w-4 h-4" />}
                  <span className="text-sm">Business hours</span>
                </div>
                <div className={`flex items-center gap-2 ${setupStatus.ai_trained ? 'text-success-600' : 'text-secondary-600'}`}>
                  {setupStatus.ai_trained ? <CheckCircle className="w-4 h-4" /> : <AlertCircle className="w-4 h-4" />}
                  <span className="text-sm">AI configuration</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Tab Navigation */}
      <div className="border-b border-secondary-200">
        <nav className="flex space-x-8">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center gap-2 py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-primary-500 text-primary-600'
                    : 'border-transparent text-secondary-500 hover:text-secondary-700 hover:border-secondary-300'
                }`}
              >
                <Icon className="w-4 h-4" />
                {tab.label}
              </button>
            );
          })}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="min-h-[400px]">
        {activeTab === 'overview' && (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Business Info */}
            <div className="lg:col-span-2 space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Business Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-secondary-600">Business Type:</span>
                      <p className="font-medium capitalize">{business.type.replace('_', ' ')}</p>
                    </div>
                    <div>
                      <span className="text-secondary-600">Phone:</span>
                      <p className="font-medium">{business.contact_phone}</p>
                    </div>
                    <div>
                      <span className="text-secondary-600">Email:</span>
                      <p className="font-medium">{business.contact_email}</p>
                    </div>
                    <div>
                      <span className="text-secondary-600">Created:</span>
                      <p className="font-medium">{new Date(business.created_at).toLocaleDateString()}</p>
                    </div>
                  </div>
                  <div>
                    <span className="text-secondary-600">Address:</span>
                    <p className="font-medium">{business.address}</p>
                  </div>
                  {business.description && (
                    <div>
                      <span className="text-secondary-600">Description:</span>
                      <p className="font-medium">{business.description}</p>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Quick Actions */}
              <Card>
                <CardHeader>
                  <CardTitle>Quick Actions</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <Button 
                    variant="outline" 
                    className="w-full justify-between"
                    onClick={() => setActiveTab('phone')}
                    icon={Phone}
                    iconPosition="left"
                  >
                    Configure Phone Integration
                    <ChevronRight className="w-4 h-4" />
                  </Button>
                  <Button 
                    variant="outline" 
                    className="w-full justify-between"
                    onClick={() => setActiveTab('calendar')}
                    icon={Calendar}
                    iconPosition="left"
                  >
                    Set Up Appointment Booking
                    <ChevronRight className="w-4 h-4" />
                  </Button>
                  <Button 
                    variant="outline" 
                    className="w-full justify-between"
                    onClick={() => setActiveTab('ai-config')}
                    icon={Headphones}
                    iconPosition="left"
                  >
                    Train AI Voice Assistant
                    <ChevronRight className="w-4 h-4" />
                  </Button>
                </CardContent>
              </Card>
            </div>

            {/* Status & Stats */}
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>AI Status</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-secondary-600">Voice Assistant</span>
                      <span className={`px-2 py-1 text-xs rounded-full ${
                        setupStatus.ai_trained 
                          ? 'bg-success-100 text-success-800' 
                          : 'bg-warning-100 text-warning-800'
                      }`}>
                        {setupStatus.ai_trained ? 'Active' : 'Setup Required'}
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-secondary-600">Phone Integration</span>
                      <span className={`px-2 py-1 text-xs rounded-full ${
                        setupStatus.phone_connected 
                          ? 'bg-success-100 text-success-800' 
                          : 'bg-warning-100 text-warning-800'
                      }`}>
                        {setupStatus.phone_connected ? 'Connected' : 'Not Connected'}
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-secondary-600">Calendar Sync</span>
                      <span className={`px-2 py-1 text-xs rounded-full ${
                        setupStatus.calendar_connected 
                          ? 'bg-success-100 text-success-800' 
                          : 'bg-warning-100 text-warning-800'
                      }`}>
                        {setupStatus.calendar_connected ? 'Synced' : 'Not Synced'}
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Recent Activity</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3 text-sm">
                    <div className="flex items-center gap-3">
                      <div className="w-2 h-2 bg-success-500 rounded-full"></div>
                      <span className="text-secondary-600">Business created</span>
                    </div>
                    <div className="flex items-center gap-3">
                      <div className="w-2 h-2 bg-warning-500 rounded-full"></div>
                      <span className="text-secondary-600">Setup in progress</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        )}

        {activeTab === 'phone' && (
          <PhoneIntegration 
            businessId={business.id} 
            onUpdate={(data) => handleIntegrationUpdate('phone', data)}
          />
        )}

        {activeTab === 'calendar' && (
          <CalendarIntegration 
            businessId={business.id}
            businessType={business.type}
            onUpdate={(data) => handleIntegrationUpdate('calendar', data)}
          />
        )}

        {activeTab === 'hours' && (
          <BusinessHours 
            businessId={business.id}
            onUpdate={(data) => handleIntegrationUpdate('hours', data)}
          />
        )}

        {activeTab === 'ai-config' && (
          <Card>
            <CardHeader>
              <CardTitle>AI Voice Configuration</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-secondary-600">
                AI configuration panel will be built here with voice training, conversation flows, and business-specific settings.
              </p>
            </CardContent>
          </Card>
        )}

        {activeTab === 'analytics' && (
          <Card>
            <CardHeader>
              <CardTitle>Analytics Dashboard</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-secondary-600">
                Analytics dashboard will show call statistics, appointment booking rates, and AI performance metrics.
              </p>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
};

export default BusinessDetail;