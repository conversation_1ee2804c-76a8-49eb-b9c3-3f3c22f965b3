# Connecto AI Voice Receptionist - Frontend

A modern React frontend for the Connecto AI Voice Receptionist platform, built with Vite, React Router, and Tailwind CSS.

## Features

- **User Authentication** - Secure login and registration with <PERSON>pa<PERSON> Auth
- **Business Management** - Create and manage business profiles
- **Agent Configuration** - Configure AI voice settings for each business
- **Call Logs & Analytics** - View call history and statistics
- **Phone System Integration** - Connect your business phone number to Connecto AI for real-time call handling and AI-driven assistance
- **Voice Preview** - Generate and preview AI voice greetings
- **Responsive Design** - Mobile-first design with Tailwind CSS
- **Real-time Updates** - Live data updates and notifications

## Technology Stack

- **React 19** - Modern React with hooks and functional components
- **Vite** - Fast build tool and development server
- **React Router 6** - Client-side routing
- **Tailwind CSS** - Utility-first CSS framework
- **Supabase** - Authentication and database
- **Axios** - HTTP client for API requests
- **React Hook Form** - Form handling and validation
- **React Hot Toast** - Toast notifications
- **Lucide React** - Beautiful icons
- **Recharts** - Charts and data visualization

## Getting Started

### Prerequisites

- Node.js 18+ and npm
- Backend API running on port 5000
- Supabase project configured

### Installation

1. **Install dependencies:**
   ```bash
   npm install
   ```

2. **Environment Setup:**
   Copy `.env.example` to `.env.local` and configure:
   ```bash
   cp .env.example .env.local
   ```

   Update the environment variables:
   ```env
   VITE_SUPABASE_URL=https://your-project-id.supabase.co
   VITE_SUPABASE_ANON_KEY=your-supabase-anon-key
   VITE_API_URL=http://localhost:5000
   ```

3. **Start development server:**
   ```bash
   npm run dev
   ```

   The app will open at `http://localhost:3000`

### Building for Production

```bash
npm run build
```

The built files will be in the `dist/` directory.

## Project Structure

```
src/
├── components/          # Reusable UI components
│   ├── common/         # Generic components (Button, Input, Card, etc.)
│   └── layout/         # Layout components (Header, Sidebar, Layout)
├── contexts/           # React contexts (Auth, etc.)
├── pages/              # Page components
├── utils/              # Utility functions and configurations
│   ├── api.js         # API client and endpoints
│   ├── constants.js   # App constants
│   ├── helpers.js     # Helper functions
│   └── supabase.js    # Supabase client
├── App.jsx            # Main app component with routing
└── main.jsx           # App entry point
```

## API Integration

The frontend integrates with the Flask backend API. See the backend README for detailed API documentation.

## Development

```bash
npm run dev      # Start development server
npm run build    # Build for production
npm run preview  # Preview production build
npm run lint     # Run ESLint
```

## License

This project is proprietary software owned by SME Analytica.
