import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// https://vite.dev/config/
export default defineConfig({
  plugins: [react()],
  server: {
    port: 3000,
    open: true,
    host: true, // Allow external connections
    allowedHosts: [
      'localhost',
      '127.0.0.1',
      '330a-89-129-214-173.ngrok-free.app', // Current ngrok URL
      '.ngrok-free.app', // Allow any ngrok subdomain
      '.ngrok.io', // Allow legacy ngrok domains
    ],
  },
  build: {
    outDir: 'dist',
    sourcemap: true,
  },
})
