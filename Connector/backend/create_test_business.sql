-- Create a test business for the development user
-- This script should be run in Supabase SQL editor

-- First, ensure the development user exists in auth.users
INSERT INTO auth.users (id, email, created_at, updated_at, email_confirmed_at, confirmed_at)
VALUES (
  '00000000-0000-0000-0000-000000000001',
  '<EMAIL>',
  now(),
  now(),
  now(),
  now()
) ON CONFLICT (id) DO NOTHING;

-- Insert a test business for the development user
INSERT INTO public.businesses (
  id,
  user_id,
  name,
  description,
  address,
  city,
  state,
  postal_code,
  phone,
  email,
  website,
  settings,
  created_at,
  updated_at
) VALUES (
  'test-business-001',
  '00000000-0000-0000-0000-000000000001',
  'Test Restaurant',
  'A test business for development',
  '123 Test Street',
  'Valencia',
  'Valencia',
  '46001',
  '+**************',
  '<EMAIL>',
  'https://test-restaurant.com',
  '{
    "hours": {
      "monday": {"open": "09:00", "close": "17:00", "closed": false},
      "tuesday": {"open": "09:00", "close": "17:00", "closed": false},
      "wednesday": {"open": "09:00", "close": "17:00", "closed": false},
      "thursday": {"open": "09:00", "close": "17:00", "closed": false},
      "friday": {"open": "09:00", "close": "17:00", "closed": false},
      "saturday": {"open": "10:00", "close": "15:00", "closed": false},
      "sunday": {"open": "10:00", "close": "15:00", "closed": true}
    },
    "services": [
      {"name": "Lunch", "description": "Mediterranean lunch menu", "price": "€15-25", "duration": "60 min"}
    ],
    "policies": {
      "appointment_policy": "Walk-ins welcome",
      "cancellation_policy": "24 hours notice required",
      "payment_methods": "Cash, Credit Cards",
      "special_instructions": "Please mention dietary restrictions"
    }
  }',
  now(),
  now()
) ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  description = EXCLUDED.description,
  updated_at = now();

-- Get the Connecto application ID
DO $$
DECLARE
  connecto_app_id UUID;
  business_id TEXT := 'test-business-001';
BEGIN
  -- Get Connecto application ID
  SELECT id INTO connecto_app_id 
  FROM public.applications 
  WHERE name = 'connecto';
  
  -- Register business with Connecto application
  INSERT INTO public.business_applications (
    business_id,
    application_id,
    is_active,
    settings,
    created_at,
    updated_at
  ) VALUES (
    business_id,
    connecto_app_id,
    true,
    '{}',
    now(),
    now()
  ) ON CONFLICT (business_id, application_id) DO UPDATE SET
    is_active = true,
    updated_at = now();
END $$; 