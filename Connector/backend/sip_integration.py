#!/usr/bin/env python3
"""
SIP Integration for Connecto AI Voice Receptionist

This module handles routing phone calls from SIP providers to LiveKit agents.
Supports LiveKit SIP Gateway integration.
"""

import os
import uuid
from typing import Optional, Dict, Any
import logging
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SIPIntegration:
    """Handles SIP integration for routing calls to LiveKit agents."""

    def __init__(self):
        self.livekit_url = os.environ.get('LIVEKIT_URL')
        self.livekit_api_key = os.environ.get('LIVEKIT_API_KEY')
        self.livekit_api_secret = os.environ.get('LIVEKIT_API_SECRET')
        self.sip_trunk_id = os.environ.get('SIP_INBOUND_TRUNK_ID')

        # Check if all required credentials are present
        if not all([self.livekit_url, self.livekit_api_key, self.livekit_api_secret]):
            logger.warning("LiveKit credentials not fully configured. SIP integration disabled.")
            self.enabled = False
        else:
            self.enabled = True
            logger.info(f"SIP Integration enabled with LiveKit: {self.livekit_url}")
            logger.info(f"SIP Trunk ID: {self.sip_trunk_id}")

    def route_call_to_agent(self, phone_number: str, business_id: str) -> Dict[str, Any]:
        """
        Route an incoming call to the appropriate LiveKit agent.

        Args:
            phone_number: Incoming caller's phone number
            business_id: Target business ID for agent configuration

        Returns:
            Dict with call routing information
        """
        if not self.enabled:
            return {"error": "SIP integration not configured - missing LiveKit credentials"}

        try:
            # Generate unique room name for this call
            room_name = f"call-{business_id}-{uuid.uuid4().hex[:8]}"

            logger.info(f"Routing call from {phone_number} to business {business_id}")
            logger.info(f"Created LiveKit room: {room_name}")

            # Start the LiveKit agent for this call
            agent_result = start_agent_for_call(business_id, room_name)

            if not agent_result.get("success"):
                logger.error(f"Failed to start agent: {agent_result.get('error')}")
                return {"error": f"Failed to start agent: {agent_result.get('error')}"}

            logger.info(f"Agent started successfully (PID: {agent_result.get('pid')})")

            return {
                "room_name": room_name,
                "room_url": f"{self.livekit_url.replace('wss://', 'https://')}/{room_name}",
                "business_id": business_id,
                "caller_number": phone_number,
                "sip_trunk_id": self.sip_trunk_id,
                "agent_pid": agent_result.get("pid"),
                "business_name": agent_result.get("business_name"),
                "business_type": agent_result.get("business_type"),
                "status": "agent_started"
            }

        except Exception as e:
            logger.error(f"Error routing call: {e}")
            return {"error": str(e)}

    def get_sip_configuration(self) -> Dict[str, Any]:
        """
        Get current SIP configuration details.

        Returns:
            Dict containing SIP configuration
        """
        return {
            "enabled": self.enabled,
            "livekit_url": self.livekit_url,
            "sip_trunk_id": self.sip_trunk_id,
            "has_api_key": bool(self.livekit_api_key),
            "has_api_secret": bool(self.livekit_api_secret)
        }

    def test_integration(self, business_id: str, test_number: str = "+**********") -> Dict[str, Any]:
        """
        Test the SIP integration without making actual calls.

        Args:
            business_id: Business ID to test with
            test_number: Test phone number

        Returns:
            Dict with test results
        """
        if not self.enabled:
            return {
                "success": False,
                "error": "SIP integration not enabled",
                "details": self.get_sip_configuration()
            }

        try:
            # Test call routing
            route_result = self.route_call_to_agent(test_number, business_id)

            if "error" in route_result:
                return {
                    "success": False,
                    "error": route_result["error"],
                    "details": self.get_sip_configuration()
                }

            return {
                "success": True,
                "message": "SIP integration test successful",
                "room_name": route_result["room_name"],
                "room_url": route_result["room_url"],
                "details": self.get_sip_configuration()
            }

        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "details": self.get_sip_configuration()
            }

def start_agent_for_call(business_id: str, room_name: str):
    """Start a Connecto agent for an incoming call."""
    try:
        import subprocess
        import os
        from db_driver import get_driver

        # Get business information from database
        try:
            driver = get_driver()
            business = driver.get_business(business_id)
            business_type = business.get('business_type', 'base')
            business_name = business.get('name', 'Business')
            logger.info(f"Starting agent for {business_name} (type: {business_type})")
        except Exception as e:
            logger.warning(f"Could not fetch business info: {e}. Using defaults.")
            business_type = 'base'
            business_name = 'Business'

        # Set up environment for the agent process
        env = os.environ.copy()
        env['BUSINESS_ID'] = business_id
        env['BUSINESS_TYPE'] = business_type
        env['BUSINESS_NAME'] = business_name
        env['LIVEKIT_ROOM'] = room_name

        # Start agent using LiveKit CLI
        agent_script = os.path.join(os.path.dirname(__file__), 'agent.py')
        if os.path.exists(agent_script):
            # Use the LiveKit CLI 'connect' command to join the specific room
            cmd = [
                'python', agent_script,
                'connect',
                '--room', room_name,
                '--business-type', business_type,
                '--business-name', business_name
            ]

            logger.info(f"Starting agent with command: {' '.join(cmd)}")

            # Start the process in background
            process = subprocess.Popen(
                cmd,
                env=env,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )

            logger.info(f"Started agent for business {business_id} in room {room_name} (PID: {process.pid})")
            return {
                "success": True,
                "pid": process.pid,
                "room_name": room_name,
                "business_id": business_id,
                "business_name": business_name,
                "business_type": business_type
            }
        else:
            logger.error(f"Agent script not found: {agent_script}")
            return {"success": False, "error": f"Agent script not found: {agent_script}"}

    except Exception as e:
        logger.error(f"Failed to start agent: {e}")
        return {"success": False, "error": str(e)}

# Example usage and testing
if __name__ == "__main__":
    sip = SIPIntegration()

    if sip.enabled:
        print("🎉 SIP Integration enabled!")
        print(f"LiveKit URL: {sip.livekit_url}")
        print(f"SIP Trunk ID: {sip.sip_trunk_id}")

        # Test the integration
        test_result = sip.test_integration("test-business-id", "+***********")
        print(f"Test result: {test_result}")
    else:
        print("❌ SIP Integration disabled - check your .env file")
        print("Required variables:")
        print("- LIVEKIT_URL")
        print("- LIVEKIT_API_KEY")
        print("- LIVEKIT_API_SECRET")
        print("- SIP_INBOUND_TRUNK_ID (optional)")