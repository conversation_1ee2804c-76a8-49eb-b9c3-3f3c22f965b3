#!/usr/bin/env python3
"""
Test script to check and create applications in the database.
"""

import os
import sys
sys.path.append(os.path.dirname(__file__))

from db_driver import get_driver

def check_and_create_applications():
    """Check what applications exist and create connecto if needed."""
    try:
        driver = get_driver()
        
        print("Checking existing applications...")
        
        # Get all applications
        response = driver.client.table("applications").select("*").execute()
        
        print(f"Found {len(response.data)} applications:")
        for app in response.data:
            print(f"  - {app.get('name', 'Unknown')} (ID: {app.get('id', 'Unknown')})")
        
        # Check if connecto exists
        connecto_exists = any(app.get('name') == 'connecto' for app in response.data)
        
        if not connecto_exists:
            print("\n🔧 Creating 'connecto' application...")
            
            # Create connecto application
            app_data = {
                "name": "connecto",
                "description": "Connecto AI Voice Receptionist"
            }
            
            result = driver.client.table("applications").insert(app_data).execute()
            
            if result.data:
                print(f"✅ Created connecto application with ID: {result.data[0]['id']}")
            else:
                print("❌ Failed to create connecto application")
        else:
            print("✅ Connecto application already exists")
            
        return True
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False

if __name__ == "__main__":
    check_and_create_applications() 