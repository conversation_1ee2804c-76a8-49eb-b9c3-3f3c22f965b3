# SME Analytica Integration for Connecto AI Voice Receptionist

This document explains how the Connecto AI Voice Receptionist integrates with SME Analytica as the parent company.

## Overview

The Connecto AI Voice Receptionist now includes information about SME Analytica, the parent company that develops and provides this AI solution. This integration allows the AI to:

1. Respond to questions about SME Analytica
2. Explain how Connecto fits into SME Analytica's broader ecosystem of business solutions
3. Provide contact information for SME Analytica
4. Promote other SME Analytica services

## SME Analytica Information

The following information about SME Analytica is included in all Connecto AI Voice Receptionist instances:

### Company Overview
SME Analytica is an AI-powered B2B SaaS startup based in Valencia, Spain, founded in 2024. The company specializes in providing analytics and business intelligence solutions tailored for small and medium-sized enterprises (SMEs), with an initial focus on the hospitality sector, particularly restaurants.

### Mission Statement
Our mission is to empower small and medium businesses with affordable, enterprise-grade AI technology that enhances efficiency, improves customer experience, and drives growth through data-informed decision making.

### Service Offerings
- Connecto AI Voice Receptionist: Intelligent phone assistant for businesses
- Restaurant Management System: Complete solution for restaurant operations including:
  * Dynamic Pricing: Adjusts prices in real-time based on demand
  * Operational Analytics: Insights to optimize business efficiency
  * Customer Behavior Insights: Analysis of customer data
  * Real-Time Traffic Tracking: Monitors customer flow
  * QR-Based Table Ordering: Contactless ordering system
  * Menu Personalization: Tailors offerings based on customer preferences
  * Order Routing: Streamlines from table orders to kitchen
  * Loyalty Incentives: Discount-based programs for repeat business
- Mobile Application: Available on TestFlight for iOS devices, providing on-the-go access to business analytics and management tools

### Company Status
SME Analytica is currently in the testing phase with its early products and is actively seeking strategic partners and investors to scale its offerings across hospitality and retail sectors.

### Leadership
Founded by Ola Yinka, who brings experience in AI and business analytics to the venture.

### Contact Information
- Website: https://smeanalytica.dev
- Restaurant Management System: https://restaurants.smeanalytica.dev
- Email: <EMAIL>

## Using SME Analytica Mode

You can run the Connecto AI Voice Receptionist in SME Analytica mode, which makes it act as the receptionist for SME Analytica itself:

```bash
python Conector/agent.py --sme-analytica dev
```

In this mode, the AI will:
- Introduce itself as the receptionist for SME Analytica
- Provide information about SME Analytica's services
- Answer questions about AI solutions for businesses
- Direct callers to the appropriate department or service

## How It Works

The SME Analytica integration works through several components:

1. **SME_ANALYTICA_INFO**: A constant in `prompt.py` that contains detailed information about SME Analytica.

2. **add_sme_analytica_info()**: A function that adds SME Analytica information to any template if it's not already included.

3. **Special SME Analytica Template**: A dedicated template for when the AI is running in SME Analytica mode.

4. **Welcome Message Modification**: All welcome messages now include "from SME Analytica" to promote brand awareness.

5. **Command Line Flag**: The `--sme-analytica` flag allows running in SME Analytica mode.

## Implementation Details

### In prompt.py

```python
# SME Analytica company information
SME_ANALYTICA_INFO = """
ABOUT SME ANALYTICA:
SME Analytica is an AI-powered B2B SaaS startup based in Valencia, Spain, founded in 2024. The company specializes in providing analytics and business intelligence solutions tailored for small and medium-sized enterprises (SMEs), with an initial focus on the hospitality sector, particularly restaurants.

COMPANY MISSION:
Our mission is to empower small and medium businesses with affordable, enterprise-grade AI technology that enhances efficiency, improves customer experience, and drives growth through data-informed decision making.

SERVICE OFFERINGS:
- Connecto AI Voice Receptionist: Intelligent phone assistant for businesses
- Restaurant Management System: Complete solution for restaurant operations including:
  * Dynamic Pricing: Adjusts prices in real-time based on demand
  * Operational Analytics: Insights to optimize business efficiency
  * Customer Behavior Insights: Analysis of customer data
  * Real-Time Traffic Tracking: Monitors customer flow
  * QR-Based Table Ordering: Contactless ordering system
  * Menu Personalization: Tailors offerings based on customer preferences
  * Order Routing: Streamlines from table orders to kitchen
  * Loyalty Incentives: Discount-based programs for repeat business
- Mobile Application: Available on TestFlight for iOS devices, providing on-the-go access to business analytics and management tools

COMPANY STATUS:
SME Analytica is currently in the testing phase with its early products and is actively seeking strategic partners and investors to scale its offerings across hospitality and retail sectors.

LEADERSHIP:
Founded by Ola Yinka, who brings experience in AI and business analytics to the venture.

CONTACT INFORMATION:
- Website: https://smeanalytica.dev
- Restaurant Management System: https://restaurants.smeanalytica.dev
- Email: <EMAIL>

If callers ask about SME Analytica or who developed this AI assistant, explain that Connecto is part of SME Analytica's suite of AI solutions designed to help small and medium businesses leverage advanced technology. You can offer to provide contact information for SME Analytica if they're interested in learning about other AI solutions for their business or would like to request a demo.
"""
```

### In agent.py

```python
# Default business settings
DEFAULT_BUSINESS_TYPE = "base"
DEFAULT_BUSINESS_NAME = "SME Analytica"

# Add SME Analytica information to business_info
business_info["sme_analytica"] = {
    "company_name": "SME Analytica",
    "company_description": "AI-powered B2B SaaS startup based in Valencia, Spain",
    "mission": "Empowering SMEs with affordable, enterprise-grade AI technology that enhances efficiency, improves customer experience, and drives growth through data-informed decision making",
    "website": "https://smeanalytica.dev",
    "restaurant_website": "https://restaurants.smeanalytica.dev",
    "email": "<EMAIL>",
    "founder": "Ola Yinka",
    "founded": "2024",
    "location": "Valencia, Spain",
    "status": "Testing phase, seeking strategic partners and investors",
    "services": [
        "Connecto AI Voice Receptionist",
        "Restaurant Management System with Dynamic Pricing",
        "Operational Analytics",
        "Customer Behavior Insights",
        "Real-Time Traffic Tracking",
        "QR-Based Table Ordering",
        "Menu Personalization",
        "Order Routing",
        "Loyalty Incentives",
        "Mobile Application (currently on TestFlight for iOS)"
    ],
    "mobile_app": {
        "available": True,
        "platform": "iOS",
        "status": "TestFlight beta testing",
        "features": "Business analytics and management tools on-the-go"
    }
}
```

## Testing SME Analytica Integration

To test the SME Analytica integration:

1. Run in SME Analytica mode:
```bash
python Conector/agent.py --sme-analytica dev
```

2. Ask questions about SME Analytica:
   - "Who developed this AI assistant?"
   - "What other services does SME Analytica offer?"
   - "How can I learn more about SME Analytica?"
   - "What is SME Analytica's mission?"
   - "Does SME Analytica have a mobile app?"
   - "How can I access the SME Analytica mobile app?"

3. Run in business-specific mode and ask about the developer:
```bash
python Conector/agent.py --business-type barbershop --business-name "Style Cuts" dev
```
Then ask:
   - "Who created this AI assistant?"
   - "What company developed Connecto?"
   - "Can you tell me about SME Analytica?"

The AI should be able to provide information about SME Analytica in all cases.

## Customizing SME Analytica Information

If you need to update the SME Analytica information, edit the `SME_ANALYTICA_INFO` constant in `prompt.py`. This will automatically update the information across all templates.

For more significant changes to how SME Analytica information is presented, you may need to modify the `add_sme_analytica_info()` function and the special SME Analytica template in the `get_template()` function.
