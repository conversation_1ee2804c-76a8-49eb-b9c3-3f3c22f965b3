# Connecto AI Voice Receptionist Platform

## Overview

Connecto is an AI-powered voice receptionist platform designed for small and medium-sized enterprises (SMEs). It provides businesses with a customizable AI assistant that can handle incoming calls, schedule appointments, answer questions, and provide information specific to the business type. The platform integrates with existing business phone numbers and adapts to various business types including restaurants, auto service centers, salons, medical offices, and more.

## Project Structure

```
Conector/
├── agent.py              # Main agent implementation using LiveKit
├── api.py                # Flask API for frontend integration
├── db_driver.py          # Supabase database driver
├── demo.py               # Demo/preview functionality
├── migrations/           # Database migration scripts
│   └── 001_initial_schema.sql
├── prompt.py             # Business-specific prompt templates
├── prompts.py            # Additional specialized prompts
├── README_DB.md          # Database documentation
└── run_demo.sh           # Script to run the demo
```

## Technology Stack

### Backend
- **Python/Flask**: API server
- **LiveKit Agents**: Voice processing framework
- **OpenAI**: Speech-to-text and text-to-speech
- **Supabase**: Database and authentication

### Frontend (To Be Implemented)
- **Next.js**: Frontend framework
- **React**: UI components
- **Supabase Auth**: User authentication
- **Tailwind CSS**: Styling

### Infrastructure
- **Docker**: Containerization
- **LiveKit SIP**: Phone system integration

## Core Components

### 1. Agent Core (`agent.py`)

The agent core handles the voice interaction between callers and the AI. It:
- Initializes with business-specific settings
- Processes speech input and generates responses
- Handles call flow based on business type
- Integrates with LiveKit for real-time audio processing

Usage:
```bash
python Conector/agent.py --business-type restaurant --business-name "Tasty Bites" --demo
```

### 2. API Layer (`api.py`)

The API provides endpoints for the frontend to interact with the Connecto platform:
- User management (registration, login)
- Business profile management
- Agent configuration
- Call logs and statistics
- Voice preview functionality

Start the API server:
```bash
python Conector/api.py
```

### 3. Database Driver (`db_driver.py`)

The database driver handles all interactions with the Supabase database:
- User authentication and profiles
- Business registration and configuration
- Agent settings storage
- Call logging and statistics

### 4. Prompt Templates (`prompt.py`)

Contains business-specific templates that define how the AI assistant behaves for different business types:
- Auto service centers
- Nail salons
- Restaurants
- Barbershops
- Medical offices
- Dental offices
- Spas
- Law firms
- Real estate offices
- Fitness centers

Each template includes:
- Role definition
- Conversation goals
- Appointment handling instructions
- Business-specific knowledge
- Welcome messages

## Database Schema

### Public Schema
- `applications`: Tracks different applications in the SME Analytica suite
- `user_profiles`: Stores user account information
- `restaurants`: Stores business profile information (used for all business types)
- `business_applications`: Links businesses to applications they're registered for

### Connecto Schema
- `agent_configs`: Stores AI voice receptionist configuration for each business
- `call_logs`: Records details of calls handled by the system

## API Endpoints

### Authentication
- `POST /users/register`: Register a new user
- `POST /users/login`: Login a user
- `GET /users/me`: Get current user profile

### Business Management
- `POST /businesses`: Create a new business
- `GET /businesses/<business_id>`: Get business details
- `PUT /businesses/<business_id>`: Update business information
- `GET /businesses/user`: Get all businesses for current user

### Agent Configuration
- `GET /agent-configs/<business_id>`: Get agent configuration
- `POST /agent-configs/<business_id>`: Create/update agent configuration

### Call Logs
- `GET /call-logs/<business_id>`: Get call logs for a business
- `GET /call-logs/<business_id>/stats`: Get call statistics
- `POST /call-logs`: Create a new call log

### Voice Preview
- `POST /preview`: Generate a voice preview for a business

## Environment Setup

### Backend Requirements

Create a `.env` file in the `Conector/` directory with:

```
# Supabase Configuration
SUPABASE_URL=https://your-project-id.supabase.co
SUPABASE_KEY=your-supabase-api-key

# OpenAI Configuration
OPENAI_API_KEY=your-openai-api-key

# LiveKit Configuration
LIVEKIT_URL=your-livekit-url
LIVEKIT_API_KEY=your-livekit-api-key
LIVEKIT_API_SECRET=your-livekit-api-secret

# Default Business Settings (optional)
BUSINESS_TYPE=restaurant
BUSINESS_NAME=Demo Restaurant
```

### Frontend Requirements

Create a `.env.local` file in the frontend directory with:

```
NEXT_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key
NEXT_PUBLIC_API_URL=http://localhost:5000
```

## Development Workflow

1. **Database Setup**
   - Create a Supabase project
   - Run the migration script in `migrations/001_initial_schema.sql`

2. **Backend Development**
   - Start the Flask API server: `python Conector/api.py`
   - Test endpoints with curl or Postman

3. **Frontend Development**
   - Create a Next.js project
   - Implement the pages and components
   - Connect to the API endpoints

4. **Testing**
   - Test voice preview functionality
   - Test business registration flow
   - Test call handling with demo mode

## Phone Integration

The Connecto platform integrates with existing business phone numbers through LiveKit's SIP capabilities. The integration process involves:

1. **SIP Trunk Configuration**
   - Set up inbound and outbound trunks
   - Configure routing rules

2. **Number Mapping**
   - Link business phone numbers to Connecto accounts
   - Set up forwarding rules

3. **Call Handling**
   - Route incoming calls to the appropriate business agent
   - Handle call transfers when needed

### Phone Configuration API

The backend exposes endpoints to configure and test business phone integration:

| Method | Endpoint                               | Description                                     |
|--------|----------------------------------------|-------------------------------------------------|
| GET    | `/businesses/{business_id}/phone`      | Retrieve current business phone number          |
| PUT    | `/businesses/{business_id}/phone`      | Update business phone number                    |
| POST   | `/businesses/{business_id}/phone/test` | Execute a test call to verify phone integration |

## Running in SaaS (multi-tenant) mode

To deploy the Connecto agent in a multi-tenant SaaS environment, start the agent worker with the `BUSINESS_ID` environment variable set for each business:

```bash
export BUSINESS_ID=<YOUR_BUSINESS_ID>
python Conector/agent.py
```

The agent will automatically fetch the business's profile and AI configuration from the database. Incoming SIP calls routed into LiveKit rooms named for the business will be answered with the correct context and voice settings.

## Frontend Implementation

The frontend for the Connecto platform should include:

1. **User Dashboard**
   - Business profile management
   - Call statistics and logs
   - Agent configuration

2. **Business Registration**
   - Multi-step form for business creation
   - Business type selection
   - Contact information collection

3. **Agent Configuration**
   - Voice selection
   - Welcome message customization
   - Business hours setup

4. **Voice Preview**
   - Generate and play sample greetings
   - Test different business types

## Support and Resources

- **Database Documentation**: Refer to README_DB.md for database details
- **LiveKit Docs**: [LiveKit Agents Documentation](https://docs.livekit.io/agents/)
- **Supabase Docs**: [Supabase Documentation](https://supabase.com/docs)

## License

This project is proprietary software owned by SME Analytica.