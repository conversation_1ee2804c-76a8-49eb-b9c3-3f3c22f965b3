#!/usr/bin/env python3
"""
Test script to verify the transferable businesses endpoint is working.
"""

import os
import sys
sys.path.append(os.path.dirname(__file__))

from db_driver import get_driver

def test_transferable_businesses():
    """Test the find_transferable_businesses method."""
    try:
        driver = get_driver()
        
        # Test with a sample user ID (you can replace this with a real user ID)
        test_user_id = "286b46f9-cea4-4d36-b342-b3a7a12ecca8"  # From the logs
        
        print(f"Testing transferable businesses for user: {test_user_id}")
        
        # Test the method
        transferable = driver.find_transferable_businesses(test_user_id)
        
        print(f"✅ Success! Found {len(transferable)} transferable businesses")
        
        if transferable:
            for business in transferable:
                print(f"  - {business.get('name', 'Unknown')} (ID: {business.get('id', 'Unknown')})")
                print(f"    Current apps: {business.get('current_applications', [])}")
                print(f"    Transfer confidence: {business.get('transfer_confidence', 'Unknown')}")
        else:
            print("  No transferable businesses found")
            
        return True
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False

if __name__ == "__main__":
    test_transferable_businesses() 