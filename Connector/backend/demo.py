"""
Connecto AI Voice Receptionist Demo

This script provides a simple demo of the Connecto AI Voice Receptionist.
It connects to a LiveKit room and plays a welcome message for the specified business type.
This is perfect for showcasing the agent to potential business clients.
"""

from __future__ import annotations
import os
import asyncio
import argparse
from livekit.agents import (
    AutoSubscribe,
    JobContext,
    WorkerOptions,
    cli
)
from livekit.plugins import openai
from dotenv import load_dotenv
from prompt import get_template, BUSINESS_TEMPLATES

# Load environment variables from .env file
dotenv_path = os.path.join(os.path.dirname(__file__), '.env')
load_dotenv(dotenv_path)
print(f"Loaded environment variables from: {dotenv_path}")

# Default business settings
DEFAULT_BUSINESS_TYPE = "auto_service"
DEFAULT_BUSINESS_NAME = "Connecto Demo Business"

async def entrypoint(ctx: JobContext):
    """Main entrypoint for the demo agent."""
    # Get command line arguments
    args = parse_arguments()
    business_type = args.business_type
    business_name = args.business_name

    # Room name is handled by the LiveKit CLI via the --room parameter

    # Get the appropriate template for this business
    template = get_template(business_type, business_name)
    instructions = template["INSTRUCTIONS"]
    welcome_message = template["WELCOME_MESSAGE"]

    # Check for OpenAI API key
    openai_api_key = os.environ.get("OPENAI_API_KEY")
    if not openai_api_key:
        print("ERROR: OpenAI API key not found!")
        print("Please set the OPENAI_API_KEY environment variable.")
        print("Example: export OPENAI_API_KEY=your-api-key-here")
        return

    print("\n" + "="*50)
    print(f"DEMO MODE: {business_type.upper()} - {business_name}")
    print("="*50)
    print(f"Welcome message: '{welcome_message}'")
    print("="*50 + "\n")

    print("Connecting to room...")
    try:
        # Connect to the room
        # Note: The room name is specified in the CLI arguments, not here
        # The --room parameter is handled by the LiveKit CLI
        await ctx.connect(auto_subscribe=AutoSubscribe.SUBSCRIBE_ALL)
        print("Connected to room successfully!")
    except Exception as e:
        print(f"Error connecting to room: {e}")
        return

    print("Checking for participants...")
    if len(ctx.room.remote_participants) > 0:
        print(f"Found {len(ctx.room.remote_participants)} participants! Playing welcome message...")
    else:
        print("No participants found, but playing welcome message anyway...")
        print("You should still hear audio if your browser is properly connected.")

    try:
        # Create a RealtimeModel with the correct parameters
        model = openai.realtime.RealtimeModel(
            model="gpt-4o-realtime-preview",
            voice="shimmer",  # Options: alloy, echo, fable, onyx, nova, shimmer
            temperature=0.8,
            api_key=openai_api_key
        )

        # Create a session from the model
        session = model.session()

        # Add event listeners for debugging
        def on_server_event(event):
            print(f"Server event received: {event.get('type', 'unknown')}")

        def on_client_event(event):
            print(f"Client event queued: {event.get('type', 'unknown')}")

        def on_speech_started(_):
            print("Speech input started!")

        def on_speech_stopped(_):
            print(f"Speech input stopped!")

        def on_transcription(event):
            print(f"Transcription completed: {event}")

        def on_generation(event):
            print(f"Generation created: {event}")

        def on_error(event):
            print(f"ERROR: {event}")

        # Register event listeners
        session.on("openai_server_event_received", on_server_event)
        session.on("openai_client_event_queued", on_client_event)
        session.on("input_speech_started", on_speech_started)
        session.on("input_speech_stopped", on_speech_stopped)
        session.on("input_audio_transcription_completed", on_transcription)
        session.on("generation_created", on_generation)
        session.on("error", on_error)

        # Update the session with modalities and instructions
        session.send_event({
            "type": "session.update",
            "session": {
                "modalities": ["text", "audio"],
                "instructions": instructions,
                "output_audio_format": "pcm16",
                "input_audio_format": "pcm16",
                "voice": "shimmer"
            }
        })

        # Create a conversation item with the welcome message
        session.send_event({
            "type": "conversation.item.create",
            "item": {
                "type": "message",
                "role": "assistant",
                "content": [{"type": "text", "text": welcome_message}]
            }
        })

        # Create a response to trigger the welcome message
        session.send_event({
            "type": "response.create"
        })

        print("\nWelcome message sent! You should hear it now.")
        print("This is what your customers will hear when they call your business.")
        print("\nAudio settings:")
        print(f"- Voice: shimmer")
        print(f"- Output format: pcm16")
        print("\nIf you can't hear anything:")
        print("1. Check your browser's audio permissions")
        print("2. Make sure your volume is turned up")
        print("3. Try a different browser or device")
        print("4. Check if your browser is muted")
        print("\nPress Ctrl+C to exit the demo when finished.")

        # Keep the demo running until manually stopped
        while True:
            await asyncio.sleep(1)

    except Exception as e:
        print(f"Error in demo: {e}")
    finally:
        print("\nDemo completed. Thank you for trying Connecto AI Voice Receptionist!")

def list_business_types():
    """List all available business types."""
    print("Available business types:")
    for business_type in sorted(BUSINESS_TEMPLATES.keys()):
        print(f"  - {business_type}")

def parse_arguments():
    """Parse command line arguments for business type and name."""
    import sys

    # Check for --list-types flag
    if "--list-types" in sys.argv:
        list_business_types()
        exit(0)

    # Get business settings from environment variables
    business_type = os.environ.get("BUSINESS_TYPE", DEFAULT_BUSINESS_TYPE)
    business_name = os.environ.get("BUSINESS_NAME", DEFAULT_BUSINESS_NAME)

    # Check for room parameter
    room_name = None
    for i, arg in enumerate(sys.argv):
        if arg == "--room" and i + 1 < len(sys.argv):
            room_name = sys.argv[i + 1]
            break

    # Also check environment variable
    if not room_name:
        room_name = os.environ.get("LIVEKIT_ROOM")

    # Validate business type
    if business_type not in BUSINESS_TEMPLATES and business_type != "base":
        print(f"Warning: Unknown business type '{business_type}'")
        print("Using base template instead. Available types are:")
        list_business_types()
        print("  - base (generic template)")
        business_type = "base"
        os.environ["BUSINESS_TYPE"] = business_type

    print(f"Starting Connecto AI Voice Receptionist Demo for: {business_name}")
    print(f"Business type: {business_type}")
    if room_name:
        print(f"Connecting to specific room: {room_name}")

    # Return a namespace object with our settings
    return argparse.Namespace(
        business_type=business_type,
        business_name=business_name,
        room_name=room_name
    )

if __name__ == "__main__":
    # Parse arguments to validate business type and print info
    args = parse_arguments()

    # Run the app
    cli.run_app(WorkerOptions(entrypoint_fnc=entrypoint))
