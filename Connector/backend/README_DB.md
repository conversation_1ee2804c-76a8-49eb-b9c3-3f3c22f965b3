# Connecto Database Driver

This document provides information about the Supabase database driver for the Connecto AI Voice Receptionist platform.

## Overview

The database driver (`db_driver.py`) handles all database operations for the Connecto platform, including:
- User authentication and management
- Business profile storage and retrieval
- Agent configuration settings
- Usage statistics and call logs

## Setup

### Prerequisites

1. Create a Supabase project at [https://supabase.com](https://supabase.com)
2. Get your Supabase URL and API key from the project settings

### Environment Variables

Add the following environment variables to your `.env` file:

```
SUPABASE_URL=https://your-project-id.supabase.co
SUPABASE_KEY=your-supabase-api-key
```

### Database Schema

Run the SQL migration script in `migrations/001_initial_schema.sql` to set up the database schema. You can do this through the Supabase web interface or using the Supabase CLI.

## Usage

### Initialization

```python
from Conector.db_driver import get_driver

# Get the driver instance
driver = get_driver()
```

### User Management

```python
# Create a new user
user = await driver.create_user(email="<EMAIL>", password="password123", full_name="<PERSON>")

# Get user information
user = await driver.get_user(user_id="user-id")

# Update user information
updated_user = await driver.update_user(user_id="user-id", data={"full_name": "John Smith"})

# Delete a user
success = await driver.delete_user(user_id="user-id")
```

### Business Profiles

```python
# Create a new business profile
business = await driver.create_business(
    user_id="user-id",
    business_data={
        "name": "Tasty Bites Restaurant",
        "type": "restaurant",
        "phone": "************",
        "email": "<EMAIL>",
        "address": "123 Main St, Anytown, USA",
        "hours": "Mon-Fri 9am-9pm, Sat-Sun 10am-10pm"
    }
)

# Get business profile
business = await driver.get_business(business_id="business-id")

# Get all businesses for a user
businesses = await driver.get_user_businesses(user_id="user-id")

# Update business profile
updated_business = await driver.update_business(
    business_id="business-id",
    data={"phone": "************"}
)

# Delete a business profile
success = await driver.delete_business(business_id="business-id")
```

### Agent Configuration

```python
# Create agent configuration
config = await driver.create_agent_config(
    business_id="business-id",
    config_data={
        "voice": "coral",
        "temperature": 0.8,
        "demo_mode": False
    }
)

# Get agent configuration
config = await driver.get_agent_config(business_id="business-id")

# Update agent configuration
updated_config = await driver.update_agent_config(
    config_id="config-id",
    config_data={
        "voice": "shimmer",
        "temperature": 0.7
    }
)
```

### Call Logging

```python
# Log a call
call_log = await driver.log_call(
    business_id="business-id",
    call_data={
        "caller_number": "************",
        "duration": 120,
        "transcript": "Call transcript here...",
        "summary": "Customer called about business hours"
    }
)

# Get call logs
logs = await driver.get_call_logs(business_id="business-id", limit=10)
```

### Integration with Agent

```python
from Conector.db_driver import configure_agent_from_db, log_agent_call

# Configure agent from database
config = await configure_agent_from_db(business_type="restaurant", business_name="Tasty Bites")

# Log a call handled by the agent
await log_agent_call(
    business_id=config["business_id"],
    caller_number="************",
    duration=120,
    transcript="Call transcript here...",
    summary="Customer called about business hours"
)
```

## Database Schema

### Users Table

Stores user account information.

| Column | Type | Description |
|--------|------|-------------|
| id | UUID | Primary key |
| email | TEXT | User's email address |
| full_name | TEXT | User's full name |
| created_at | TIMESTAMP | Creation timestamp |
| updated_at | TIMESTAMP | Last update timestamp |

### Businesses Table

Stores business profile information.

| Column | Type | Description |
|--------|------|-------------|
| id | UUID | Primary key |
| user_id | UUID | Foreign key to users table |
| name | TEXT | Business name |
| type | TEXT | Business type (e.g., restaurant, auto_service) |
| phone | TEXT | Business phone number |
| email | TEXT | Business email address |
| address | TEXT | Business physical address |
| hours | TEXT | Business hours of operation |
| created_at | TIMESTAMP | Creation timestamp |
| updated_at | TIMESTAMP | Last update timestamp |

### Agent Configs Table

Stores agent configuration settings.

| Column | Type | Description |
|--------|------|-------------|
| id | UUID | Primary key |
| business_id | UUID | Foreign key to businesses table |
| config | JSONB | Agent configuration as JSON |
| created_at | TIMESTAMP | Creation timestamp |
| updated_at | TIMESTAMP | Last update timestamp |

### Call Logs Table

Stores call logs.

| Column | Type | Description |
|--------|------|-------------|
| id | UUID | Primary key |
| business_id | UUID | Foreign key to businesses table |
| timestamp | TIMESTAMP | Call timestamp |
| caller_number | TEXT | Caller's phone number |
| duration | INTEGER | Call duration in seconds |
| transcript | TEXT | Call transcript |
| summary | TEXT | Call summary |
