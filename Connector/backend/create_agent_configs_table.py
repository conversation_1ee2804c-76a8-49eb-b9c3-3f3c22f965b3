#!/usr/bin/env python3
"""
Create the agent_configs table in Supabase
"""

from supabase import create_client
import os
from dotenv import load_dotenv

load_dotenv()

def create_agent_configs_table():
    supabase = create_client(os.getenv('SUPABASE_URL'), os.getenv('SUPABASE_KEY'))
    
    try:
        # Try to create a test record to see if table exists
        test_result = supabase.table('agent_configs').select('*').limit(1).execute()
        print('✅ Agent configs table already exists!')
        return True
    except Exception as e:
        if 'does not exist' in str(e):
            print('⚠️ Table does not exist. Please create it manually in Supabase SQL editor:')
            print('''
            -- Execute this SQL in Supabase SQL Editor:
            
            CREATE TABLE IF NOT EXISTS public.agent_configs (
                id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
                business_id UUID NOT NULL REFERENCES public.businesses(id) ON DELETE CASCADE,
                config JSONB DEFAULT '{}',
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                UNIQUE(business_id)
            );
            
            -- Enable RLS
            ALTER TABLE public.agent_configs ENABLE ROW LEVEL SECURITY;
            
            -- Policy for anon users (API access)
            DROP POLICY IF EXISTS "agent_configs_anon_access" ON public.agent_configs;
            CREATE POLICY "agent_configs_anon_access" ON public.agent_configs
            FOR ALL TO anon USING (true) WITH CHECK (true);
            
            -- Policy for authenticated users  
            DROP POLICY IF EXISTS "agent_configs_user_access" ON public.agent_configs;
            CREATE POLICY "agent_configs_user_access" ON public.agent_configs
            FOR ALL TO authenticated 
            USING (business_id IN (SELECT id FROM public.businesses WHERE user_id = auth.uid()))
            WITH CHECK (business_id IN (SELECT id FROM public.businesses WHERE user_id = auth.uid()));
            ''')
            return False
        else:
            print(f'❌ Unexpected error: {e}')
            return False

if __name__ == '__main__':
    create_agent_configs_table() 