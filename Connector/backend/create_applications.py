#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to create the required applications in the database.
This script should be run once to initialize the applications table.
"""

import os
from db_driver import get_driver

def create_applications():
    """Create the required applications in the database."""
    driver = get_driver()
    
    applications = [
        {
            "name": "connecto",
            "description": "Connecto AI Voice Receptionist - AI-powered voice assistant for businesses"
        },
        {
            "name": "main", 
            "description": "SME Analytica Main App - Comprehensive business analytics platform"
        },
        {
            "name": "ros",
            "description": "Restaurant Ordering Services - Online ordering system for restaurants"
        },
        {
            "name": "restaurant_mgmt",
            "description": "Restaurant Management - Legacy restaurant management system"
        }
    ]
    
    created_apps = []
    
    for app_data in applications:
        try:
            # Check if application already exists
            existing = driver.client.table("applications").select("*").eq("name", app_data["name"]).execute()
            
            if len(existing.data) > 0:
                print(f"✅ Application '{app_data['name']}' already exists with ID: {existing.data[0]['id']}")
                created_apps.append(existing.data[0])
            else:
                # Try to create the application
                try:
                    result = driver.client.table("applications").insert(app_data).execute()
                    print(f"✅ Created application '{app_data['name']}' with ID: {result.data[0]['id']}")
                    created_apps.append(result.data[0])
                except Exception as e:
                    if "row-level security" in str(e).lower():
                        print(f"⚠️  Cannot create application '{app_data['name']}' due to RLS policy. Please create manually in Supabase dashboard.")
                        print(f"   SQL: INSERT INTO applications (name, description) VALUES ('{app_data['name']}', '{app_data['description']}');")
                    else:
                        print(f"❌ Error creating application '{app_data['name']}': {e}")
                        
        except Exception as e:
            print(f"❌ Error checking/creating application '{app_data['name']}': {e}")
    
    print(f"\n📊 Summary: {len(created_apps)} applications available")
    return created_apps

if __name__ == "__main__":
    print("🚀 Creating required applications...")
    create_applications() 