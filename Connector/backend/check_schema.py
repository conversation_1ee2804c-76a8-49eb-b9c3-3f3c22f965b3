#!/usr/bin/env python3
"""
Script to check database schema and existing data.
"""

import os
import sys
sys.path.append(os.path.dirname(__file__))

from db_driver import get_driver

def check_schema():
    """Check database schema and existing data."""
    try:
        driver = get_driver()
        
        print("Checking database schema and data...")
        
        # Check restaurants table
        print("\n📊 Restaurants table:")
        try:
            response = driver.client.table("restaurants").select("id, name, user_id").limit(5).execute()
            print(f"  Found {len(response.data)} restaurants:")
            for restaurant in response.data:
                print(f"    - {restaurant.get('name', 'Unknown')} (ID: {restaurant.get('id', 'Unknown')}, User: {restaurant.get('user_id', 'Unknown')})")
        except Exception as e:
            print(f"  ❌ Error accessing restaurants: {str(e)}")
        
        # Check applications table
        print("\n📱 Applications table:")
        try:
            response = driver.client.table("applications").select("*").limit(5).execute()
            print(f"  Found {len(response.data)} applications:")
            for app in response.data:
                print(f"    - {app.get('name', 'Unknown')} (ID: {app.get('id', 'Unknown')})")
        except Exception as e:
            print(f"  ❌ Error accessing applications: {str(e)}")
        
        # Check business_applications table
        print("\n🔗 Business Applications table:")
        try:
            response = driver.client.table("business_applications").select("*").limit(5).execute()
            print(f"  Found {len(response.data)} business-application links:")
            for link in response.data:
                print(f"    - Business: {link.get('business_id', 'Unknown')}, App: {link.get('application_id', 'Unknown')}")
        except Exception as e:
            print(f"  ❌ Error accessing business_applications: {str(e)}")
        
        # Check user_profiles table
        print("\n👤 User Profiles table:")
        try:
            response = driver.client.table("user_profiles").select("id, email, full_name").limit(5).execute()
            print(f"  Found {len(response.data)} user profiles:")
            for user in response.data:
                print(f"    - {user.get('email', 'Unknown')} (ID: {user.get('id', 'Unknown')})")
        except Exception as e:
            print(f"  ❌ Error accessing user_profiles: {str(e)}")
            
        return True
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False

if __name__ == "__main__":
    check_schema() 