# Customizing Connecto AI Voice Receptionist

This document explains how to customize the Connecto AI Voice Receptionist for different business types.

## Overview

The Connecto AI Voice Receptionist can be customized with business-specific information such as:
- Business hours
- Service prices
- Contact information
- Special offers and discounts
- And more

These customizations make the AI voice receptionist more accurate and helpful for your specific business needs.

## Customization Methods

There are three ways to customize the Connecto AI Voice Receptionist:

1. **Command Line Arguments**: Pass customization parameters when starting the agent
2. **Environment Variables**: Set environment variables before starting the agent
3. **Database Configuration**: Store customization in the Supabase database (for production use)

## Command Line Customization

You can customize the agent by passing command line arguments when starting it:

```bash
python Conector/agent.py --business-type barbershop --business-name "Style Cuts" \
  --business-hours "Mon-Fri: 9am-7pm, Sat: 8am-5pm, Sun: Closed" \
  --business-phone "************" \
  --barbershop-haircut-price "€30-€40" \
  --barbershop-hot-towel-price "€35" \
  dev
```

Remember to include the subcommand (`dev`, `connect`, `console`, or `start`) at the end.

## Available Customization Parameters

### Common Parameters (All Business Types)

| Parameter | Description | Example |
|-----------|-------------|---------|
| `--business-type` | Type of business | `auto_service`, `barbershop`, `restaurant` |
| `--business-name` | Name of the business | `"Style Cuts Barbershop"` |
| `--business-phone` | Business phone number | `"************"` |
| `--business-email` | Business email address | `"<EMAIL>"` |
| `--business-address` | Physical address | `"123 Main St, Anytown"` |
| `--business-hours` | Operating hours | `"Mon-Fri: 9am-7pm, Sat: 8am-5pm"` |

### Auto Service Center Parameters

| Parameter | Description | Default |
|-----------|-------------|---------|
| `--auto-oil-change-price` | Price for oil changes | `"€49.99-€89.99"` |
| `--auto-tire-rotation-price` | Price for tire rotation | `"€25"` |
| `--auto-brake-service-price` | Price for brake service | `"requires inspection for accurate quote"` |
| `--auto-diagnostic-fee` | Fee for diagnostics | `"€89.99"` |

### Barbershop Parameters

| Parameter | Description | Default |
|-----------|-------------|---------|
| `--barbershop-haircut-price` | Price for standard haircut | `"€25-€35"` |
| `--barbershop-haircut-beard-price` | Price for haircut & beard trim | `"€40-€50"` |
| `--barbershop-hot-towel-price` | Price for hot towel shave | `"€30"` |
| `--barbershop-kids-haircut-price` | Price for kids haircut | `"€20"` |
| `--barbershop-senior-haircut-price` | Price for senior haircut | `"€20"` |

### Nail Salon Parameters

| Parameter | Description | Default |
|-----------|-------------|---------|
| `--nail-basic-manicure-price` | Price for basic manicure | `"€25"` |
| `--nail-gel-manicure-price` | Price for gel manicure | `"€35"` |
| `--nail-basic-pedicure-price` | Price for basic pedicure | `"€35"` |
| `--nail-deluxe-pedicure-price` | Price for deluxe pedicure | `"€50"` |
| `--nail-acrylics-price` | Price for full set acrylics | `"€45-€60"` |
| `--nail-art-price` | Price for nail art | `"€5-€15"` |
| `--nail-new-client-discount` | Discount for new clients | `"10%"` |

## Environment Variables

You can also set environment variables to customize the agent. This is useful for Docker deployments or when using a `.env` file:

```
# Basic business information
BUSINESS_TYPE=barbershop
BUSINESS_NAME=Style Cuts
BUSINESS_PHONE=************
BUSINESS_EMAIL=<EMAIL>
BUSINESS_ADDRESS=123 Main St, Anytown
BUSINESS_HOURS=Mon-Fri: 9am-7pm, Sat: 8am-5pm, Sun: Closed

# Barbershop specific settings
BARBERSHOP_HAIRCUT_PRICE=€30-€40
BARBERSHOP_HAIRCUT_BEARD_PRICE=€45-€55
BARBERSHOP_HOT_TOWEL_PRICE=€35
BARBERSHOP_KIDS_HAIRCUT_PRICE=€22
BARBERSHOP_SENIOR_HAIRCUT_PRICE=€22
```

## Database Configuration

For production use, you can store customization settings in the Supabase database. This allows for easy updates through the admin interface.

The database schema includes:
- `restaurants` table for basic business information
- `agent_configs` table for AI voice receptionist configuration

Example of creating a customized business profile:

```python
from db_driver import get_driver

driver = get_driver()

# Create business profile
business = driver.create_business(
    user_id="user-id",
    business_data={
        "name": "Style Cuts Barbershop",
        "type": "barbershop",
        "contact_phone": "************",
        "contact_email": "<EMAIL>",
        "address": "123 Main St, Anytown",
        "opening_hours": {"Mon-Fri": "9am-7pm", "Sat": "8am-5pm", "Sun": "Closed"}
    }
)

# Configure agent with custom settings
driver.create_agent_config(
    business_id=business["id"],
    config_data={
        "services": {
            "haircut_price": "€30-€40",
            "haircut_beard_price": "€45-€55",
            "hot_towel_price": "€35",
            "kids_haircut_price": "€22",
            "senior_haircut_price": "€22"
        },
        "voice": "coral",
        "temperature": 0.7
    }
)
```

## Adding New Customizable Fields

To add new customizable fields:

1. Update the `get_template` function in `prompt.py` to handle the new field
2. Add the field to the appropriate template in `prompt.py`
3. Update the `entrypoint` function in `agent.py` to collect the field from environment variables
4. Add command line argument handling in `parse_arguments` function

## Testing Customizations

You can test your customizations using the demo mode:

```bash
python Conector/agent.py --business-type barbershop --business-name "Style Cuts" \
  --barbershop-haircut-price "€30-€40" --demo dev
```

This will start the agent in demo mode with your custom settings.

## Multi-tenant SaaS mode

To run the agent for a specific business in a multi-tenant setup, set the `BUSINESS_ID` environment variable:

```bash
export BUSINESS_ID=<BUSINESS_ID>
python Conector/agent.py
```

This instructs the agent to fetch the business's context and AI settings from the database automatically.
