-- Initialize applications table with required applications
-- Run this script in the Supabase SQL editor or as a migration

INSERT INTO applications (name, description) VALUES 
('connecto', 'Connecto AI Voice Receptionist - AI-powered voice assistant for businesses'),
('main', 'SME Analytica Main App - Comprehensive business analytics platform'),
('ros', 'Restaurant Ordering Services - Online ordering system for restaurants'),
('restaurant_mgmt', 'Restaurant Management - Legacy restaurant management system')
ON CONFLICT (name) DO NOTHING;

-- Verify the applications were created
SELECT id, name, description, created_at FROM applications ORDER BY name; 