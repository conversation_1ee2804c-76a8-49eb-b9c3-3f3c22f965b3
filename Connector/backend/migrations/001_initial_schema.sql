-- Enhanced database schema for Connecto AI Voice Receptionist
-- This migration creates the necessary tables for integrating with the existing SME Analytica database.
-- It creates a dedicated schema for Connecto and adds junction tables to link with existing restaurant data.

-- Create a dedicated schema for Connecto
CREATE SCHEMA IF NOT EXISTS connecto;

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create applications table in public schema to track different applications
CREATE TABLE IF NOT EXISTS public.applications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT UNIQUE NOT NULL,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert our known applications
INSERT INTO public.applications (name, description)
VALUES
    ('connecto', 'Connecto AI Voice Receptionist Platform'),
    ('restaurant_mgmt', 'Restaurant Management System')
ON CONFLICT (name) DO NOTHING;

-- Create business_applications junction table in public schema
-- This links existing restaurants to different applications
CREATE TABLE IF NOT EXISTS public.business_applications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    business_id UUID NOT NULL,
    application_id UUID NOT NULL REFERENCES public.applications(id) ON DELETE CASCADE,
    is_active BOOLEAN DEFAULT TRUE,
    settings JSONB DEFAULT '{}'::JSONB, -- Application-specific settings
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(business_id, application_id)
);

-- Add foreign key constraint to link to existing restaurants table
-- Using ALTER TABLE to handle the case where the table already exists
ALTER TABLE public.business_applications
ADD CONSTRAINT fk_business_applications_business
FOREIGN KEY (business_id)
REFERENCES public.restaurants(id)
ON DELETE CASCADE;

-- Create agent_configs table in connecto schema
CREATE TABLE IF NOT EXISTS connecto.agent_configs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    business_id UUID NOT NULL,
    config JSONB NOT NULL DEFAULT '{}'::JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(business_id)
);

-- Add foreign key constraint to link to existing restaurants table
ALTER TABLE connecto.agent_configs
ADD CONSTRAINT fk_agent_configs_business
FOREIGN KEY (business_id)
REFERENCES public.restaurants(id)
ON DELETE CASCADE;

-- Create call_logs table in connecto schema
CREATE TABLE IF NOT EXISTS connecto.call_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    business_id UUID NOT NULL,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    caller_number TEXT,
    duration INTEGER,
    transcript TEXT,
    summary TEXT
);

-- Add foreign key constraint to link to existing restaurants table
ALTER TABLE connecto.call_logs
ADD CONSTRAINT fk_call_logs_business
FOREIGN KEY (business_id)
REFERENCES public.restaurants(id)
ON DELETE CASCADE;

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_business_applications_business_id ON public.business_applications(business_id);
CREATE INDEX IF NOT EXISTS idx_business_applications_application_id ON public.business_applications(application_id);
CREATE INDEX IF NOT EXISTS idx_agent_configs_business_id ON connecto.agent_configs(business_id);
CREATE INDEX IF NOT EXISTS idx_call_logs_business_id ON connecto.call_logs(business_id);
CREATE INDEX IF NOT EXISTS idx_call_logs_timestamp ON connecto.call_logs(timestamp);

-- Enable Row Level Security (RLS) on new tables
ALTER TABLE public.applications ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.business_applications ENABLE ROW LEVEL SECURITY;
ALTER TABLE connecto.agent_configs ENABLE ROW LEVEL SECURITY;
ALTER TABLE connecto.call_logs ENABLE ROW LEVEL SECURITY;

-- Create policies for applications table (admin only)
CREATE POLICY applications_select_all ON public.applications
    FOR SELECT
    USING (TRUE);

-- Create policies for business_applications table
CREATE POLICY business_applications_select_own ON public.business_applications
    FOR SELECT
    USING (EXISTS (
        SELECT 1 FROM public.restaurants
        WHERE restaurants.id = business_applications.business_id
        AND restaurants.user_id = auth.uid()
    ));

CREATE POLICY business_applications_insert_own ON public.business_applications
    FOR INSERT
    WITH CHECK (EXISTS (
        SELECT 1 FROM public.restaurants
        WHERE restaurants.id = business_applications.business_id
        AND restaurants.user_id = auth.uid()
    ));

CREATE POLICY business_applications_update_own ON public.business_applications
    FOR UPDATE
    USING (EXISTS (
        SELECT 1 FROM public.restaurants
        WHERE restaurants.id = business_applications.business_id
        AND restaurants.user_id = auth.uid()
    ));

CREATE POLICY business_applications_delete_own ON public.business_applications
    FOR DELETE
    USING (EXISTS (
        SELECT 1 FROM public.restaurants
        WHERE restaurants.id = business_applications.business_id
        AND restaurants.user_id = auth.uid()
    ));

-- Create policies for connecto.agent_configs table
CREATE POLICY agent_configs_select_own ON connecto.agent_configs
    FOR SELECT
    USING (EXISTS (
        SELECT 1 FROM public.restaurants
        WHERE restaurants.id = agent_configs.business_id
        AND restaurants.user_id = auth.uid()
    ));

CREATE POLICY agent_configs_insert_own ON connecto.agent_configs
    FOR INSERT
    WITH CHECK (EXISTS (
        SELECT 1 FROM public.restaurants
        WHERE restaurants.id = agent_configs.business_id
        AND restaurants.user_id = auth.uid()
    ));

CREATE POLICY agent_configs_update_own ON connecto.agent_configs
    FOR UPDATE
    USING (EXISTS (
        SELECT 1 FROM public.restaurants
        WHERE restaurants.id = agent_configs.business_id
        AND restaurants.user_id = auth.uid()
    ));

CREATE POLICY agent_configs_delete_own ON connecto.agent_configs
    FOR DELETE
    USING (EXISTS (
        SELECT 1 FROM public.restaurants
        WHERE restaurants.id = agent_configs.business_id
        AND restaurants.user_id = auth.uid()
    ));

-- Create policies for connecto.call_logs table
CREATE POLICY call_logs_select_own ON connecto.call_logs
    FOR SELECT
    USING (EXISTS (
        SELECT 1 FROM public.restaurants
        WHERE restaurants.id = call_logs.business_id
        AND restaurants.user_id = auth.uid()
    ));

CREATE POLICY call_logs_insert_own ON connecto.call_logs
    FOR INSERT
    WITH CHECK (EXISTS (
        SELECT 1 FROM public.restaurants
        WHERE restaurants.id = call_logs.business_id
        AND restaurants.user_id = auth.uid()
    ));

-- Create a function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers to automatically update the updated_at column
CREATE TRIGGER update_applications_updated_at
BEFORE UPDATE ON public.applications
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_business_applications_updated_at
BEFORE UPDATE ON public.business_applications
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_agent_configs_updated_at
BEFORE UPDATE ON connecto.agent_configs
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- Create a view to easily get restaurants registered with Connecto
CREATE OR REPLACE VIEW connecto.registered_restaurants AS
SELECT r.*, ba.settings as connecto_settings
FROM public.restaurants r
JOIN public.business_applications ba ON r.id = ba.business_id
JOIN public.applications a ON ba.application_id = a.id
WHERE a.name = 'connecto' AND ba.is_active = TRUE;
