-- Create AI Training Data table for storing business-specific AI training configurations
-- Migration: create_ai_training_data_table.sql
-- Date: January 2025

-- Create the ai_training_data table
CREATE TABLE IF NOT EXISTS public.ai_training_data (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    business_id UUID NOT NULL REFERENCES public.businesses(id) ON DELETE CASCADE,
    
    -- Training data fields (stored as JSON)
    faqs JSONB DEFAULT '[]'::jsonb,
    greetings JSONB DEFAULT '{}'::jsonb,
    conversation_flows JSONB DEFAULT '[]'::jsonb,
    business_info JSONB DEFAULT '{}'::jsonb,
    
    -- <PERSON><PERSON><PERSON>
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Ensure one training record per business
    UNIQUE(business_id)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_ai_training_data_business_id ON public.ai_training_data(business_id);
CREATE INDEX IF NOT EXISTS idx_ai_training_data_updated_at ON public.ai_training_data(updated_at);

-- Enable Row Level Security (RLS)
ALTER TABLE public.ai_training_data ENABLE ROW LEVEL SECURITY;

-- RLS Policies for ai_training_data table

-- 1. Users can only access training data for their own businesses
CREATE POLICY "Users can view their own AI training data" ON public.ai_training_data
    FOR SELECT
    USING (
        business_id IN (
            SELECT id FROM public.businesses WHERE user_id = auth.uid()
        )
    );

-- 2. Users can only insert training data for their own businesses
CREATE POLICY "Users can create AI training data for their businesses" ON public.ai_training_data
    FOR INSERT
    WITH CHECK (
        business_id IN (
            SELECT id FROM public.businesses WHERE user_id = auth.uid()
        )
    );

-- 3. Users can only update training data for their own businesses
CREATE POLICY "Users can update their own AI training data" ON public.ai_training_data
    FOR UPDATE
    USING (
        business_id IN (
            SELECT id FROM public.businesses WHERE user_id = auth.uid()
        )
    )
    WITH CHECK (
        business_id IN (
            SELECT id FROM public.businesses WHERE user_id = auth.uid()
        )
    );

-- 4. Users can only delete training data for their own businesses
CREATE POLICY "Users can delete their own AI training data" ON public.ai_training_data
    FOR DELETE
    USING (
        business_id IN (
            SELECT id FROM public.businesses WHERE user_id = auth.uid()
        )
    );

-- 5. Allow anon users (API) to read/write for system operations
CREATE POLICY "API anon access to AI training data" ON public.ai_training_data
    FOR ALL
    TO anon
    USING (true)
    WITH CHECK (true);

-- Create updated_at trigger
CREATE OR REPLACE FUNCTION public.handle_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Add trigger to automatically update updated_at
CREATE TRIGGER set_ai_training_data_updated_at
    BEFORE UPDATE ON public.ai_training_data
    FOR EACH ROW
    EXECUTE FUNCTION public.handle_updated_at();

-- Grant permissions
GRANT ALL ON public.ai_training_data TO anon;
GRANT ALL ON public.ai_training_data TO authenticated;

-- Add comments for documentation
COMMENT ON TABLE public.ai_training_data IS 'Stores AI training data and configurations for each business including FAQs, greetings, conversation flows, and business information';
COMMENT ON COLUMN public.ai_training_data.business_id IS 'Foreign key reference to the businesses table';
COMMENT ON COLUMN public.ai_training_data.faqs IS 'JSON array of frequently asked questions and answers';
COMMENT ON COLUMN public.ai_training_data.greetings IS 'JSON object containing different greeting messages (main, afterHours, busy, voicemail)';
COMMENT ON COLUMN public.ai_training_data.conversation_flows IS 'JSON array of conversation flow configurations for different scenarios';
COMMENT ON COLUMN public.ai_training_data.business_info IS 'JSON object containing business-specific information like services, policies, pricing, hours, etc.'; 