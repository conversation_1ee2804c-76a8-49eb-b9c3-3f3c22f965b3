#!/bin/bash

# Simple shell script to run the Connecto demo with different business types

# Default values
BUSINESS_TYPE="auto_service"
BUSINESS_NAME="Connecto Demo Business"
LIVEKIT_COMMAND="dev"
ROOM_NAME=""

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  case $1 in
    --business-type)
      BUSINESS_TYPE="$2"
      shift 2
      ;;
    --business-name)
      BUSINESS_NAME="$2"
      shift 2
      ;;
    --list-types)
      python3 ./demo.py --list-types
      exit 0
      ;;
    --api-key)
      export OPENAI_API_KEY="$2"
      shift 2
      ;;
    --room)
      ROOM_NAME="$2"
      shift 2
      ;;
    *)
      LIVEKIT_COMMAND="$1"
      shift
      ;;
  esac
done

# Export environment variables
export BUSINESS_TYPE="$BUSINESS_TYPE"
export BUSINESS_NAME="$BUSINESS_NAME"

# Set room name if provided
if [ -n "$ROOM_NAME" ]; then
  export LIVEKIT_ROOM="$ROOM_NAME"
  ROOM_ARGS="--room $ROOM_NAME"
  echo "Connecting to specific room: $ROOM_NAME"
else
  ROOM_ARGS=""
fi

# Print info
echo "Starting Connecto AI Voice Receptionist Demo"
echo "Business Type: $BUSINESS_TYPE"
echo "Business Name: $BUSINESS_NAME"
echo "LiveKit Command: $LIVEKIT_COMMAND"
echo

# Get the directory of this script
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"

# Run the demo
if [ -n "$ROOM_NAME" ]; then
  # For the 'dev' command, we need to use the connect subcommand with --room
  if [ "$LIVEKIT_COMMAND" = "dev" ]; then
    python3 "$SCRIPT_DIR/demo.py" connect --room "$ROOM_NAME"
  else
    python3 "$SCRIPT_DIR/demo.py" $LIVEKIT_COMMAND $ROOM_ARGS
  fi
else
  python3 "$SCRIPT_DIR/demo.py" $LIVEKIT_COMMAND
fi
