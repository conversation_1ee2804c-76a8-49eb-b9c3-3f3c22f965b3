"""
Supabase Database Driver for Connecto AI Voice Receptionist.

This module handles all database operations for the Connecto platform, including:
- User authentication and management
- Business profile storage and retrieval
- Agent configuration settings
- Usage statistics and logs

The driver uses Supabase as the backend database service.
"""

import os
import json
import logging
from typing import Dict, List, Optional, Any
from dotenv import load_dotenv
from supabase import create_client, Client

# Load environment variables
dotenv_path = os.path.join(os.path.dirname(__file__), '.env')
load_dotenv(dotenv_path)

# Configure logging
logger = logging.getLogger("connecto-db")

# Supabase configuration
SUPABASE_URL = os.environ.get("SUPABASE_URL")
SUPABASE_KEY = os.environ.get("SUPABASE_KEY")

# Schema names
SCHEMA_PUBLIC = "public"
SCHEMA_CONNECTO = "connecto"

# Table names
TABLE_APPLICATIONS = "applications"
TABLE_USERS = "auth.users"
TABLE_BUSINESSES = "businesses"  # Unified table for all business types
TABLE_BUSINESS_APPLICATIONS = "business_applications"  # Fixed: using correct table name from migration schema
TABLE_AGENT_CONFIGS = "agent_configs"
TABLE_CALL_LOGS = "call_logs"
TABLE_BUSINESS_TYPES = "business_types"

# Application names
APP_CONNECTO = "connecto"
APP_RESTAURANT_MGMT = "restaurant_mgmt"
APP_ROS = "ros"  # Restaurant Ordering Services
APP_MAIN = "main"  # Main SME Analytica app

class SupabaseDriver:
    """
    Supabase database driver for Connecto AI Voice Receptionist.
    Handles all database operations including CRUD for users, businesses, and agent configurations.
    """

    def __init__(self, url: Optional[str] = None, key: Optional[str] = None):
        """
        Initialize the Supabase client.

        Args:
            url: Supabase project URL (optional, defaults to environment variable)
            key: Supabase API key (optional, defaults to environment variable)
        """
        self.url = url or SUPABASE_URL
        self.key = key or SUPABASE_KEY

        if not self.url or not self.key:
            raise ValueError(
                "Supabase URL and API key must be provided either as arguments "
                "or as environment variables (SUPABASE_URL, SUPABASE_KEY)"
            )

        # Initialize Supabase client
        self.client = create_client(self.url, self.key)

    # User Management Functions

    def create_user(self, email: str, password: str, full_name: str) -> Dict[str, Any]:
        """
        Create a new user account.

        Args:
            email: User's email address
            password: User's password
            full_name: User's full name

        Returns:
            Dict containing user information
        """
        try:
            # Create user in Supabase Auth
            self.client.auth.sign_up({
                "email": email,
                "password": password
            })

            # Generate a UUID for the user
            import uuid
            user_id = str(uuid.uuid4())

            # Check if user profile already exists
            existing_profile = self.client.table(TABLE_USERS).select("*").eq("id", user_id).execute()

            if len(existing_profile.data) > 0:
                return {
                    "user_id": user_id,
                    "email": email,
                    "full_name": existing_profile.data[0].get("full_name", full_name)
                }

            # Add user to user_profiles table with additional information
            user_data = {
                "id": user_id,
                "email": email,
                "full_name": full_name,
                "created_at": "now()",
                "updated_at": "now()"
            }

            self.client.table(TABLE_USERS).insert(user_data).execute()

            return {"user_id": user_id, "email": email, "full_name": full_name}

        except Exception as e:
            raise Exception(f"Failed to create user: {str(e)}")

    def get_user(self, user_id: str) -> Dict[str, Any]:
        """
        Get user information by ID.

        Args:
            user_id: The user's unique ID

        Returns:
            Dict containing user information
        """
        try:
            response = self.client.table(TABLE_USERS).select("*").eq("id", user_id).execute()

            if len(response.data) == 0:
                raise Exception(f"User with ID {user_id} not found")

            return response.data[0]

        except Exception as e:
            raise Exception(f"Failed to get user: {str(e)}")

    def update_user(self, user_id: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Update user information.

        Args:
            user_id: The user's unique ID
            data: Dict containing fields to update

        Returns:
            Dict containing updated user information
        """
        try:
            # Add updated_at timestamp
            update_data = {**data, "updated_at": "now()"}

            response = self.client.table(TABLE_USERS).update(update_data).eq("id", user_id).execute()

            if len(response.data) == 0:
                raise Exception(f"User with ID {user_id} not found")

            return response.data[0]

        except Exception as e:
            raise Exception(f"Failed to update user: {str(e)}")

    def delete_user(self, user_id: str) -> bool:
        """
        Delete a user account.

        Args:
            user_id: The user's unique ID

        Returns:
            True if successful
        """
        try:
            # Delete from users table
            self.client.table(TABLE_USERS).delete().eq("id", user_id).execute()

            # Delete user from Supabase Auth
            self.client.auth.admin.delete_user(user_id)

            return True

        except Exception as e:
            raise Exception(f"Failed to delete user: {str(e)}")

    # Application Management Functions

    def get_application_id(self, app_name: str) -> str:
        """
        Get the application ID by name.

        Args:
            app_name: The application name (e.g., 'connecto')

        Returns:
            The application ID
        """
        try:
            response = self.client.table(TABLE_APPLICATIONS).select("id").eq("name", app_name).execute()

            if len(response.data) == 0:
                raise Exception(f"Application with name '{app_name}' not found")

            return response.data[0]["id"]

        except Exception as e:
            raise Exception(f"Failed to get application ID: {str(e)}")

    # Business Profile Functions

    def find_business_by_name_type(self, name: str, business_type: str) -> Optional[Dict[str, Any]]:
        """
        Find business by name and type.

        Args:
            name: Business name
            business_type: Type of business

        Returns:
            Business profile if found, None otherwise
        """
        try:
            response = self.client.table(TABLE_BUSINESSES).select("*").eq("name", name).execute()

            if len(response.data) > 0:
                return response.data[0]
            return None

        except Exception as e:
            logger.error(f"Error finding business by name and type: {str(e)}")
            return None

    def find_business_by_phone(self, phone_number: str) -> Optional[Dict[str, Any]]:
        """
        Find business by phone number for SIP call routing.

        Args:
            phone_number: Business phone number to lookup

        Returns:
            Business profile if found, None otherwise
        """
        try:
            # Clean up phone number for comparison (remove formatting)
            clean_phone = ''.join(filter(str.isdigit, phone_number))

            # Search by exact phone match first
            response = self.client.table(TABLE_BUSINESSES).select("*").eq("phone", phone_number).execute()

            if len(response.data) > 0:
                return response.data[0]

            # If no exact match, try to find with cleaned digits
            all_businesses_response = self.client.table(TABLE_BUSINESSES).select("id, phone, name").execute()

            for business in all_businesses_response.data:
                if business.get('phone'):
                    business_clean_phone = ''.join(filter(str.isdigit, business['phone']))
                    if business_clean_phone == clean_phone:
                        # Get full business details
                        full_business_response = self.client.table(TABLE_BUSINESSES).select("*").eq("id", business['id']).execute()
                        if len(full_business_response.data) > 0:
                            return full_business_response.data[0]

            return None

        except Exception as e:
            logger.error(f"Error finding business by phone number {phone_number}: {str(e)}")
            return None

    def create_business(self, user_id: str, business_data: Dict[str, Any], app_name: str = APP_CONNECTO) -> Dict[str, Any]:
        """
        Create a new business profile and register it with the specified application.

        Args:
            user_id: The user's unique ID
            business_data: Dict containing business information
            app_name: The application name to register with (default: 'connecto')

        Returns:
            Dict containing the created business profile
        """
        try:
            # Ensure required fields are present
            required_fields = ["name"]
            for field in required_fields:
                if field not in business_data:
                    raise ValueError(f"Missing required field: {field}")

            # Check if business already exists (by name only)
            existing_business = self.find_business_by_name_type(
                business_data["name"],
                business_data.get("business_type", "restaurant")
            )

            if existing_business:
                # Business already exists, register it with the application
                business_id = existing_business["id"]
                print(f"Business already exists with ID: {business_id}")

                # Check if already registered with this application
                app_id = self.get_application_id(app_name)
                self.register_business_with_app(business_id, app_id)

                return existing_business

            # Separate database fields from settings fields
            # These are the actual database columns
            db_fields = {
                "name", "description", "address", "city", "state", "postal_code",
                "phone", "email", "website", "hours", "timezone"
            }

            # Extract database fields
            db_data = {k: v for k, v in business_data.items() if k in db_fields}

            # Extract settings fields (everything else)
            settings_data = {k: v for k, v in business_data.items() if k not in db_fields}

            # Prepare database insertion data
            data = {
                "user_id": user_id,
                "created_at": "now()",
                "updated_at": "now()",
                "settings": settings_data if settings_data else {},
                **db_data
            }

            # Create new business
            response = self.client.table(TABLE_BUSINESSES).insert(data).execute()
            business = response.data[0]

            # Register with application
            app_id = self.get_application_id(app_name)
            self.register_business_with_app(business["id"], app_id)

            return business

        except Exception as e:
            raise Exception(f"Failed to create business profile: {str(e)}")

    def register_business_with_app(self, business_id: str, app_id: str, settings: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Register a business with an application.

        Args:
            business_id: The business's unique ID
            app_id: The application's unique ID
            settings: Application-specific settings (optional)

        Returns:
            Dict containing the registration information
        """
        try:
            # Check if already registered
            response = self.client.table(TABLE_BUSINESS_APPLICATIONS) \
                .select("*") \
                .eq("business_id", business_id) \
                .eq("application_id", app_id) \
                .execute()

            if len(response.data) > 0:
                # Already registered, update settings if provided
                if settings:
                    return self.client.table(TABLE_BUSINESS_APPLICATIONS) \
                        .update({"settings": settings, "is_active": True}) \
                        .eq("id", response.data[0]["id"]) \
                        .execute() \
                        .data[0]
                return response.data[0]

            # Register with application
            data = {
                "business_id": business_id,
                "application_id": app_id,
                "is_active": True,
                "settings": settings or {},
                "created_at": "now()",
                "updated_at": "now()"
            }

            response = self.client.table(TABLE_BUSINESS_APPLICATIONS).insert(data).execute()
            return response.data[0]

        except Exception as e:
            raise Exception(f"Failed to register business with application: {str(e)}")

    def get_business(self, business_id: str) -> Dict[str, Any]:
        """
        Get business profile by ID.

        Args:
            business_id: The business's unique ID

        Returns:
            Dict containing business information
        """
        try:
            response = self.client.table(TABLE_BUSINESSES).select("*").eq("id", business_id).execute()

            if len(response.data) == 0:
                raise Exception(f"Business with ID {business_id} not found")

            business = response.data[0]

            # Extract complex fields from settings and merge with business data
            settings = business.get('settings', {}) or {}

            # Remove settings from the main business object to avoid duplication
            business_without_settings = {k: v for k, v in business.items() if k != 'settings'}

            # Merge settings back into the main business object
            merged_business = {**business_without_settings, **settings}

            # Try to include AI configuration if it exists (skip if table doesn't exist)
            try:
                agent_config = self.get_agent_config(business_id)
                if agent_config and 'config' in agent_config:
                    merged_business['ai_config'] = agent_config['config']
            except Exception as e:
                # Check if it's a "table does not exist" error
                if 'does not exist' in str(e) or 'relation' in str(e):
                    logger.info(f"Agent configs table doesn't exist yet, skipping AI config for business {business_id}")
                else:
                    logger.info(f"No AI config found for business {business_id}: {str(e)}")

                # Set default AI config structure for frontend
                merged_business['ai_config'] = {
                    'greeting_message': '',
                    'voice_tone': 'professional',
                    'can_book_appointments': True,
                    'can_provide_pricing': True,
                    'can_provide_hours': True,
                    'transfer_conditions': ''
                }

            return merged_business

        except Exception as e:
            logger.error(f"Error getting business {business_id}: {str(e)}")
            raise Exception(f"Failed to get business profile: {str(e)}")

    def get_business_with_app_settings(self, business_id: str, app_name: str = APP_CONNECTO) -> Dict[str, Any]:
        """
        Get business profile with application-specific settings.

        Args:
            business_id: The business's unique ID
            app_name: The application name (default: 'connecto')

        Returns:
            Dict containing business information with app settings
        """
        try:
            # Get business profile
            business = self.get_business(business_id)

            # Get application ID
            app_id = self.get_application_id(app_name)

            # Get application settings
            response = self.client.table(TABLE_BUSINESS_APPLICATIONS) \
                .select("settings") \
                .eq("business_id", business_id) \
                .eq("application_id", app_id) \
                .execute()

            if len(response.data) > 0:
                business["app_settings"] = response.data[0]["settings"]
            else:
                business["app_settings"] = {}

            return business

        except Exception as e:
            raise Exception(f"Failed to get business with app settings: {str(e)}")

    def get_user_businesses(self, user_id: str, app_name: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Get all businesses owned by a user, optionally filtered by application.

        Args:
            user_id: The user's unique ID
            app_name: Filter by application name (optional)

        Returns:
            List of business profiles
        """
        try:
            if app_name:
                # Simple approach: just get user businesses without complex joins
                response = self.client.table(TABLE_BUSINESSES) \
                    .select("*") \
                    .eq("user_id", user_id) \
                    .execute()

                logger.info(f"Found {len(response.data)} businesses for user {user_id}")
                return response.data

            else:
                # Get all businesses for user
                response = self.client.table(TABLE_BUSINESSES) \
                    .select("*") \
                    .eq("user_id", user_id) \
                    .execute()

                logger.info(f"Found {len(response.data)} total businesses for user {user_id}")
                return response.data

        except Exception as e:
            logger.error(f"Error getting user businesses: {str(e)}")
            return []

    def update_business(self, business_id: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Update business profile.

        Args:
            business_id: The business's unique ID
            data: Dict containing fields to update

        Returns:
            Dict containing updated business information
        """
        try:
            # Extract AI configuration if present
            ai_config = data.pop('ai_config', None)

            # Define which fields go directly to the businesses table
            direct_fields = {
                'name', 'description', 'email', 'phone', 'website',
                'address', 'city', 'state', 'postal_code', 'country',
                'timezone', 'currency', 'logo_url', 'banner_url', 'is_active'
            }

            # Separate direct fields from complex fields
            update_data = {}
            settings_data = {}

            for key, value in data.items():
                if key in direct_fields:
                    update_data[key] = value
                else:
                    # Store complex fields (hours, services, policies, etc.) in settings
                    settings_data[key] = value

            # Get existing settings to merge with new data
            try:
                existing_business = self.client.table(TABLE_BUSINESSES).select("settings").eq("id", business_id).execute()
                if existing_business.data:
                    existing_settings = existing_business.data[0].get('settings', {}) or {}
                    # Merge existing settings with new settings data
                    merged_settings = {**existing_settings, **settings_data}
                    if merged_settings:  # Only update settings if there's data
                        update_data['settings'] = merged_settings
                else:
                    if settings_data:  # Only add settings if there's data
                        update_data['settings'] = settings_data
            except Exception as e:
                logger.warning(f"Could not get existing settings, using new data only: {str(e)}")
                if settings_data:
                    update_data['settings'] = settings_data

            # Add updated_at timestamp
            update_data["updated_at"] = "now()"

            # Update the business data - Supabase Python client doesn't return data by default on updates
            response = self.client.table(TABLE_BUSINESSES).update(update_data).eq("id", business_id).execute()

            # Use get_business method to ensure we get properly formatted data with settings merged
            logger.info(f"Business {business_id} update completed, fetching properly formatted data")
            try:
                # Use the get_business method which properly merges settings and adds AI config defaults
                updated_business = self.get_business(business_id)
            except Exception as e:
                logger.error(f"Failed to fetch business after update: {str(e)}")
                # Fallback: return minimal response with the update data
                updated_business = {"id": business_id, **update_data}

            # Handle AI configuration separately if provided
            if ai_config:
                try:
                    self.update_agent_config_by_business(business_id, ai_config)
                    # Add ai_config back to response for frontend consistency
                    updated_business['ai_config'] = ai_config
                except Exception as e:
                    # Check if it's a "table does not exist" error
                    if 'does not exist' in str(e) or 'relation' in str(e):
                        logger.warning(f"Agent configs table doesn't exist yet, skipping AI config save: {str(e)}")
                        # Still add ai_config to response for frontend consistency
                        updated_business['ai_config'] = ai_config
                    else:
                        logger.warning(f"Failed to update AI config: {str(e)}")
                        # Don't fail the whole update if AI config fails

            logger.info(f"Business {business_id} updated successfully")
            return updated_business

        except Exception as e:
            logger.error(f"Error updating business {business_id}: {str(e)}")
            raise Exception(f"Failed to update business profile: {str(e)}")

    def update_business_app_settings(self, business_id: str, app_name: str, settings: Dict[str, Any]) -> Dict[str, Any]:
        """
        Update application-specific settings for a business.

        Args:
            business_id: The business's unique ID
            app_name: The application name
            settings: Application-specific settings

        Returns:
            Dict containing updated settings
        """
        try:
            # Get application ID
            app_id = self.get_application_id(app_name)

            # Get business application record
            response = self.client.table(TABLE_BUSINESS_APPLICATIONS) \
                .select("id, settings") \
                .eq("business_id", business_id) \
                .eq("application_id", app_id) \
                .execute()

            if len(response.data) == 0:
                # Not registered with this application yet
                return self.register_business_with_app(business_id, app_id, settings)

            # Update settings
            record_id = response.data[0]["id"]
            current_settings = response.data[0]["settings"]

            # Merge settings
            merged_settings = {**current_settings, **settings}

            response = self.client.table(TABLE_BUSINESS_APPLICATIONS) \
                .update({"settings": merged_settings, "updated_at": "now()"}) \
                .eq("id", record_id) \
                .execute()

            return response.data[0]

        except Exception as e:
            raise Exception(f"Failed to update business app settings: {str(e)}")

    def delete_business(self, business_id: str) -> bool:
        """
        Delete a business profile.

        Args:
            business_id: The business's unique ID

        Returns:
            True if successful
        """
        try:
            self.client.table(TABLE_BUSINESSES).delete().eq("id", business_id).execute()
            return True

        except Exception as e:
            raise Exception(f"Failed to delete business profile: {str(e)}")

    def deactivate_business_app(self, business_id: str, app_name: str) -> bool:
        """
        Deactivate a business for a specific application.

        Args:
            business_id: The business's unique ID
            app_name: The application name

        Returns:
            True if successful
        """
        try:
            # Get application ID
            app_id = self.get_application_id(app_name)

            # Update is_active flag
            self.client.table(TABLE_BUSINESS_APPLICATIONS) \
                .update({"is_active": False, "updated_at": "now()"}) \
                .eq("business_id", business_id) \
                .eq("application_id", app_id) \
                .execute()

            return True

        except Exception as e:
            raise Exception(f"Failed to deactivate business app: {str(e)}")

    # Agent Configuration Functions

    def create_agent_config(self, business_id: str, config_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create a new agent configuration for a business.

        Args:
            business_id: The business's unique ID
            config_data: Dict containing agent configuration

        Returns:
            Dict containing the created agent configuration
        """
        try:
            # First, ensure the business is registered with Connecto
            app_id = self.get_application_id(APP_CONNECTO)
            self.register_business_with_app(business_id, app_id)

            # Check if config already exists
            existing = self.client.table(TABLE_AGENT_CONFIGS).select("*").eq("business_id", business_id).execute()
            if len(existing.data) > 0:
                # Update existing config
                return self.update_agent_config(existing.data[0]["id"], config_data)

            # Prepare config data
            data = {
                "business_id": business_id,
                "created_at": "now()",
                "updated_at": "now()",
                "config": json.dumps(config_data)
            }

            response = self.client.table(TABLE_AGENT_CONFIGS).insert(data).execute()

            # Parse the JSON config in the response
            result = response.data[0]
            if isinstance(result["config"], str):
                result["config"] = json.loads(result["config"])

            return result

        except Exception as e:
            raise Exception(f"Failed to create agent configuration: {str(e)}")

    def get_agent_config(self, business_id: str) -> Dict[str, Any]:
        """
        Get agent configuration for a business.

        Args:
            business_id: The business's unique ID

        Returns:
            Dict containing agent configuration
        """
        try:
            response = self.client.table(TABLE_AGENT_CONFIGS).select("*").eq("business_id", business_id).execute()

            if len(response.data) == 0:
                raise Exception(f"Agent configuration for business ID {business_id} not found")

            # Parse the JSON config
            config = response.data[0]
            if isinstance(config["config"], str):
                config["config"] = json.loads(config["config"])

            return config

        except Exception as e:
            raise Exception(f"Failed to get agent configuration: {str(e)}")

    def get_or_create_agent_config(self, business_id: str, default_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Get agent configuration for a business or create it if it doesn't exist.

        Args:
            business_id: The business's unique ID
            default_config: Default configuration to use if none exists

        Returns:
            Dict containing agent configuration
        """
        try:
            response = self.client.table(TABLE_AGENT_CONFIGS).select("*").eq("business_id", business_id).execute()

            if len(response.data) == 0:
                # Create new config
                return self.create_agent_config(business_id, default_config)

            # Parse the JSON config
            config = response.data[0]
            if isinstance(config["config"], str):
                config["config"] = json.loads(config["config"])

            return config

        except Exception as e:
            raise Exception(f"Failed to get or create agent configuration: {str(e)}")

    def update_agent_config(self, config_id: str, config_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Update agent configuration.

        Args:
            config_id: The configuration's unique ID
            config_data: Dict containing configuration to update

        Returns:
            Dict containing updated agent configuration
        """
        try:
            # Add updated_at timestamp
            update_data = {
                "updated_at": "now()",
                "config": json.dumps(config_data)
            }

            response = self.client.table(TABLE_AGENT_CONFIGS).update(update_data).eq("id", config_id).execute()

            if len(response.data) == 0:
                raise Exception(f"Agent configuration with ID {config_id} not found")

            # Parse the JSON config
            config = response.data[0]
            if isinstance(config["config"], str):
                config["config"] = json.loads(config["config"])

            return config

        except Exception as e:
            raise Exception(f"Failed to update agent configuration: {str(e)}")

    def update_agent_config_by_business(self, business_id: str, config_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Update agent configuration by business ID.

        Args:
            business_id: The business's unique ID
            config_data: Dict containing configuration to update

        Returns:
            Dict containing updated agent configuration
        """
        try:
            # Get existing config
            response = self.client.table(TABLE_AGENT_CONFIGS).select("*").eq("business_id", business_id).execute()

            if len(response.data) == 0:
                # Create new config
                return self.create_agent_config(business_id, config_data)

            # Update existing config
            config_id = response.data[0]["id"]
            current_config = json.loads(response.data[0]["config"]) if isinstance(response.data[0]["config"], str) else response.data[0]["config"]

            # Merge configs
            merged_config = {**current_config, **config_data}

            return self.update_agent_config(config_id, merged_config)

        except Exception as e:
            raise Exception(f"Failed to update agent configuration: {str(e)}")

    # Call Logging Functions

    def log_call(self, business_id: str, call_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Log a call to the database.

        Args:
            business_id: The business's unique ID
            call_data: Dict containing call information

        Returns:
            Dict containing the created call log
        """
        try:
            # First, ensure the business is registered with Connecto
            app_id = self.get_application_id(APP_CONNECTO)
            self.register_business_with_app(business_id, app_id)

            # Prepare call data
            data = {
                "business_id": business_id,
                "timestamp": "now()",
                **call_data
            }

            response = self.client.table(TABLE_CALL_LOGS).insert(data).execute()

            return response.data[0]

        except Exception as e:
            raise Exception(f"Failed to log call: {str(e)}")

    def get_call_logs(self, business_id: str, limit: int = 100, offset: int = 0) -> List[Dict[str, Any]]:
        """
        Get call logs for a business.

        Args:
            business_id: The business's unique ID
            limit: Maximum number of logs to return
            offset: Number of logs to skip

        Returns:
            List of call logs
        """
        try:
            response = self.client.table(TABLE_CALL_LOGS) \
                .select("*") \
                .eq("business_id", business_id) \
                .order("timestamp", desc=True) \
                .range(offset, offset + limit - 1) \
                .execute()

            return response.data

        except Exception as e:
            raise Exception(f"Failed to get call logs: {str(e)}")

    def get_call_stats(self, business_id: str, days: int = 30) -> Dict[str, Any]:
        """
        Get call statistics for a business.

        Args:
            business_id: The business's unique ID
            days: Number of days to include in stats

        Returns:
            Dict containing call statistics
        """
        try:
            from datetime import datetime, timedelta

            # Calculate the date threshold
            date_threshold = (datetime.now() - timedelta(days=days)).isoformat()

            # Get all calls for the business within the time period
            response = self.client.table(TABLE_CALL_LOGS) \
                .select("*") \
                .eq("business_id", business_id) \
                .gte("timestamp", date_threshold) \
                .execute()

            calls = response.data if response.data else []

            # Calculate statistics
            total_calls = len(calls)

            if total_calls == 0:
                return {
                    "total_calls": 0,
                    "avg_duration": 0,
                    "max_duration": 0,
                    "min_duration": 0,
                    "calls_by_day": []
                }

            # Calculate duration statistics
            durations = [call.get("duration", 0) for call in calls if call.get("duration") is not None]

            if durations:
                avg_duration = sum(durations) / len(durations)
                max_duration = max(durations)
                min_duration = min(durations)
            else:
                avg_duration = max_duration = min_duration = 0

            # Group calls by day
            calls_by_day = {}
            for call in calls:
                if call.get("timestamp"):
                    # Parse timestamp and get date
                    call_date = datetime.fromisoformat(call["timestamp"].replace('Z', '+00:00')).date()
                    date_str = call_date.isoformat()

                    if date_str not in calls_by_day:
                        calls_by_day[date_str] = 0
                    calls_by_day[date_str] += 1

            # Convert to list format sorted by date (most recent first)
            calls_by_day_list = [
                {"day": date, "calls": count}
                for date, count in sorted(calls_by_day.items(), reverse=True)
            ]

            return {
                "total_calls": total_calls,
                "avg_duration": round(avg_duration, 2),
                "max_duration": max_duration,
                "min_duration": min_duration,
                "calls_by_day": calls_by_day_list
            }

        except Exception as e:
            raise Exception(f"Failed to get call statistics: {str(e)}")

    def find_transferable_businesses(self, user_id: str, target_app: str = APP_CONNECTO) -> List[Dict[str, Any]]:
        """
        Find businesses owned by the user that could be transferred to the target application.

        Args:
            user_id: The user's unique ID
            target_app: The target application name (default: 'connecto')

        Returns:
            List of businesses that could be transferred
        """
        try:
            # Get target application ID
            target_app_id = self.get_application_id(target_app)

            # Find all businesses owned by the user
            user_businesses = self.client.table(TABLE_BUSINESSES) \
                .select("*") \
                .eq("user_id", user_id) \
                .execute()

            transferable_businesses = []

            for business in user_businesses.data:
                # Check if business is already registered with target app
                existing_registration = self.client.table(TABLE_BUSINESS_APPLICATIONS) \
                    .select("*") \
                    .eq("business_id", business["id"]) \
                    .eq("application_id", target_app_id) \
                    .execute()

                if len(existing_registration.data) == 0:
                    # Business is not registered with target app - it's transferable

                    # Get current app registrations (simplified query)
                    current_apps_response = self.client.table(TABLE_BUSINESS_APPLICATIONS) \
                        .select("application_id") \
                        .eq("business_id", business["id"]) \
                        .eq("is_active", True) \
                        .execute()

                    # Get application names for current registrations
                    current_applications = []
                    for app_reg in current_apps_response.data:
                        try:
                            app_response = self.client.table(TABLE_APPLICATIONS) \
                                .select("name") \
                                .eq("id", app_reg["application_id"]) \
                                .execute()
                            if len(app_response.data) > 0:
                                current_applications.append(app_response.data[0]["name"])
                        except Exception as e:
                            print(f"Warning: Could not get application name: {str(e)}")
                            continue

                    business_info = {
                        **business,
                        "current_applications": current_applications,
                        "transfer_confidence": self._calculate_transfer_confidence(business, target_app)
                    }

                    transferable_businesses.append(business_info)

            return transferable_businesses

        except Exception as e:
            raise Exception(f"Failed to find transferable businesses: {str(e)}")

    def _calculate_transfer_confidence(self, business: Dict[str, Any], target_app: str) -> str:
        """
        Calculate confidence level for business transfer based on completeness of data.

        Args:
            business: Business data
            target_app: Target application

        Returns:
            Confidence level: 'high', 'medium', 'low'
        """
        score = 0

        # Check data completeness
        if business.get("name"): score += 2
        if business.get("contact_email"): score += 2
        if business.get("contact_phone"): score += 1
        if business.get("address"): score += 1
        if business.get("description"): score += 1
        if business.get("opening_hours"): score += 1

        if score >= 6:
            return "high"
        elif score >= 4:
            return "medium"
        else:
            return "low"

    def transfer_business_to_app(self, business_id: str, target_app: str = APP_CONNECTO,
                                settings: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Transfer/register an existing business to a new application.

        Args:
            business_id: The business's unique ID
            target_app: The target application name
            settings: Application-specific settings

        Returns:
            Dict containing the registration information
        """
        try:
            # Get target application ID
            target_app_id = self.get_application_id(target_app)

            # Check if business exists
            business = self.get_business(business_id)
            if not business:
                raise Exception("Business not found")

            # Check if already registered
            existing = self.client.table(TABLE_BUSINESS_APPLICATIONS) \
                .select("*") \
                .eq("business_id", business_id) \
                .eq("application_id", target_app_id) \
                .execute()

            if len(existing.data) > 0:
                return existing.data[0]

            # Register business with new application
            registration_data = {
                "business_id": business_id,
                "application_id": target_app_id,
                "is_active": True,
                "settings": settings or {}
            }

            response = self.client.table(TABLE_BUSINESS_APPLICATIONS) \
                .insert(registration_data) \
                .execute()

            # Create default agent config for Connecto
            if target_app == APP_CONNECTO:
                try:
                    self.get_or_create_agent_config(business_id, {
                        "voice": "shimmer",
                        "temperature": 0.8,
                        "demo_mode": False
                    })
                except Exception as e:
                    print(f"Warning: Could not create agent config: {str(e)}")

            return response.data[0]

        except Exception as e:
            raise Exception(f"Failed to transfer business: {str(e)}")

    # AI Training Data Management Functions

    def get_ai_training_data(self, business_id: str) -> Optional[Dict[str, Any]]:
        """
        Get AI training data for a business.

        Args:
            business_id: The business's unique ID

        Returns:
            Dict containing AI training data or None if not found
        """
        try:
            response = self.client.table("ai_training_data").select("*").eq("business_id", business_id).execute()

            if len(response.data) == 0:
                return None

            training_record = response.data[0]

            # Parse JSON fields
            training_data = {
                "business_id": business_id,
                "faqs": json.loads(training_record.get("faqs", "[]")),
                "greetings": json.loads(training_record.get("greetings", "{}")),
                "conversationFlows": json.loads(training_record.get("conversation_flows", "[]")),
                "businessInfo": json.loads(training_record.get("business_info", "{}")),
                "created_at": training_record.get("created_at"),
                "updated_at": training_record.get("updated_at")
            }

            return training_data

        except Exception as e:
            logger.error(f"Failed to get AI training data: {str(e)}")
            return None

    def save_ai_training_data(self, business_id: str, training_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Save AI training data for a business.

        Args:
            business_id: The business's unique ID
            training_data: Dict containing training data

        Returns:
            Dict containing saved training data
        """
        try:
            # Prepare data for database storage
            db_data = {
                "business_id": business_id,
                "faqs": json.dumps(training_data.get("faqs", [])),
                "greetings": json.dumps(training_data.get("greetings", {})),
                "conversation_flows": json.dumps(training_data.get("conversationFlows", [])),
                "business_info": json.dumps(training_data.get("businessInfo", {})),
                "updated_at": "now()"
            }

            # Check if training data already exists
            existing = self.client.table("ai_training_data").select("id").eq("business_id", business_id).execute()

            if len(existing.data) > 0:
                # Update existing record
                response = self.client.table("ai_training_data").update(db_data).eq("business_id", business_id).execute()
            else:
                # Create new record
                db_data["created_at"] = "now()"
                response = self.client.table("ai_training_data").insert(db_data).execute()

            if len(response.data) == 0:
                raise Exception("Failed to save training data")

            # Return the training data in the expected format
            return training_data

        except Exception as e:
            raise Exception(f"Failed to save AI training data: {str(e)}")

    def create_ai_training_table_if_not_exists(self):
        """
        Create the ai_training_data table if it doesn't exist.
        This method can be called during setup.
        """
        try:
            # This would typically be done through Supabase migrations
            # For now, we'll assume the table exists or handle the error gracefully
            logger.info("AI training data table should be created through Supabase migrations")
            pass
        except Exception as e:
            logger.error(f"Failed to create AI training table: {str(e)}")


# Create a singleton instance
_driver = None

def get_driver() -> SupabaseDriver:
    """
    Get the Supabase driver instance.

    Returns:
        SupabaseDriver instance
    """
    global _driver
    if _driver is None:
        _driver = SupabaseDriver()
    return _driver


# Utility functions for integration with agent.py

def get_business_by_name(business_name: str, app_name: str = APP_CONNECTO) -> Optional[Dict[str, Any]]:
    """
    Find a business by name that is registered with a specific application.

    Args:
        business_name: Name of the business
        app_name: Application name to filter by (default: 'connecto')

    Returns:
        Business profile or None if not found
    """
    try:
        driver = get_driver()

        # First try to find by exact match
        response = driver.client.table("businesses").select("*, business_types(name, description)").eq("name", business_name).execute()

        if len(response.data) > 0:
            business = response.data[0]

            # Check if registered with the application
            app_id = driver.get_application_id(app_name)
            app_response = driver.client.table("business_applications") \
                .select("*") \
                .eq("business_id", business["id"]) \
                .eq("application_id", app_id) \
                .eq("is_active", True) \
                .execute()

            if len(app_response.data) > 0:
                return business

        # If not found or not registered, try a more flexible search using Supabase query builder
        businesses_response = driver.client.table("businesses") \
            .select("*, business_types(name, description), business_applications!inner(application_id, applications(name))") \
            .ilike("name", f"%{business_name}%") \
            .eq("business_applications.applications.name", app_name) \
            .eq("business_applications.is_active", True) \
            .limit(1) \
            .execute()

        if businesses_response.data and len(businesses_response.data) > 0:
            return businesses_response.data[0]

        return None
    except Exception as e:
        print(f"Error finding business: {str(e)}")
        return None


def load_business_config(business_id: str, app_name: str = APP_CONNECTO) -> Dict[str, Any]:
    """
    Load business configuration for use with the agent.

    Args:
        business_id: The business's unique ID
        app_name: Application name (default: 'connecto')

    Returns:
        Dict containing business and agent configuration
    """
    driver = get_driver()

    # Get business profile with app settings
    business = driver.get_business_with_app_settings(business_id, app_name)

    # Get agent configuration
    try:
        agent_config = driver.get_or_create_agent_config(business_id, {
            "voice": "shimmer",
            "temperature": 0.8,
            "demo_mode": False
        })
        config = agent_config["config"]
    except Exception as e:
        print(f"Error getting agent config: {str(e)}")
        # Use default configuration if none exists
        config = {
            "voice": "shimmer",
            "temperature": 0.8,
            "demo_mode": False
        }

    # Combine business profile and agent configuration
    return {
        "business_id": business_id,
        "business_name": business["name"],
        "business_phone": business.get("phone"),
        "business_email": business.get("email"),
        "business_address": business.get("address"),
        "business_hours": business.get("settings", {}).get("opening_hours"),
        "business_type": business.get("business_types", {}).get("name") if business.get("business_types") else None,
        "app_settings": business.get("app_settings", {}),
        "agent_config": config
    }


def configure_agent_from_db(business_id: Optional[str] = None,
                           business_name: Optional[str] = None,
                           app_name: str = APP_CONNECTO,
                           user_id: Optional[str] = None,
                           create_if_missing: bool = False,
                           business_type: Optional[str] = None) -> Dict[str, Any]:
    """
    Configure the agent using database settings.
    This function can be called from agent.py to load business-specific settings.

    Args:
        business_id: The business's unique ID (optional)
        business_name: Name of the business (optional)
        app_name: Application name (default: 'connecto')
        user_id: User ID for creating a new business if needed (optional)
        create_if_missing: Whether to create a new business if not found (default: False)
        business_type: Business type for new businesses (optional)

    Returns:
        Dict containing business and agent configuration
    """
    driver = get_driver()

    # If business_id is provided, use it directly
    if business_id:
        # Check if business exists and is registered with the application
        try:
            return load_business_config(business_id, app_name)
        except Exception as e:
            print(f"Error loading business config: {str(e)}")
            if not create_if_missing:
                raise

    # If business_name is provided, look up the business
    if business_name:
        business = get_business_by_name(business_name, app_name)
        if business:
            return load_business_config(business["id"], app_name)

        # Business not found, create it if requested
        if create_if_missing and user_id:
            try:
                # Get business type ID (default to restaurant if not specified)
                business_type_name = business_type or "restaurant"
                business_type_response = driver.client.table("business_types") \
                    .select("id") \
                    .eq("name", business_type_name) \
                    .execute()

                if len(business_type_response.data) == 0:
                    # Default to restaurant type if specified type not found
                    business_type_response = driver.client.table("business_types") \
                        .select("id") \
                        .eq("name", "restaurant") \
                        .execute()

                business_type_id = business_type_response.data[0]["id"] if business_type_response.data else None

                # Create new business entry
                business_data = {
                    "name": business_name,
                    "email": "<EMAIL>",  # Required field
                    "address": "Demo Address",  # Required field
                    "user_id": user_id,
                    "business_type_id": business_type_id,
                    "is_active": True
                }

                # Create business using the driver method
                business = driver.create_business(user_id, business_data, app_name)

                return load_business_config(business["id"], app_name)
            except Exception as e:
                print(f"Error creating business: {str(e)}")

    # Return default configuration if no business found
    return {
        "business_id": None,
        "business_name": business_name or "Demo Business",
        "business_phone": None,
        "business_email": None,
        "business_address": None,
        "business_type": business_type,
        "app_settings": {},
        "agent_config": {
            "voice": "shimmer",
            "temperature": 0.8,
            "demo_mode": False
        }
    }


def log_agent_call(business_id: str,
                  caller_number: Optional[str] = None,
                  duration: Optional[int] = None,
                  transcript: Optional[str] = None,
                  summary: Optional[str] = None,
                  call_id: Optional[str] = None,
                  direction: Optional[str] = None,
                  status: Optional[str] = None,
                  room_name: Optional[str] = None) -> Dict[str, Any]:
    """
    Log a call handled by the agent.

    Args:
        business_id: The business's unique ID
        caller_number: Phone number of the caller (optional)
        duration: Call duration in seconds (optional)
        transcript: Call transcript (optional)
        summary: Call summary (optional)
        call_id: Unique identifier for the call (optional)
        direction: Call direction ('inbound' or 'outbound') (optional)
        status: Call status (e.g., 'initiated', 'completed', 'failed') (optional)
        room_name: LiveKit room name for the call (optional)

    Returns:
        Dict containing the created call log
    """
    driver = get_driver()

    call_data = {
        "caller_number": caller_number,
        "duration": duration,
        "transcript": transcript,
        "summary": summary,
        "call_id": call_id,
        "direction": direction,
        "status": status,
        "room_name": room_name
    }

    # Filter out None values
    call_data = {k: v for k, v in call_data.items() if v is not None}

    return driver.log_call(business_id, call_data)


def register_business_for_connecto(business_id: str, settings: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """
    Register an existing business for Connecto services.

    Args:
        business_id: The business's unique ID
        settings: Connecto-specific settings (optional)

    Returns:
        Dict containing the registration information
    """
    driver = get_driver()
    app_id = driver.get_application_id(APP_CONNECTO)
    return driver.register_business_with_app(business_id, app_id, settings)


def get_business_stats(business_id: str, days: int = 30) -> Dict[str, Any]:
    """
    Get comprehensive statistics for a business.

    Args:
        business_id: The business's unique ID
        days: Number of days to include in stats (default: 30)

    Returns:
        Dict containing business statistics
    """
    driver = get_driver()

    # Get business profile
    business = driver.get_business_with_app_settings(business_id, APP_CONNECTO)

    # Get call statistics
    call_stats = driver.get_call_stats(business_id, days)

    # Get recent calls
    recent_calls = driver.get_call_logs(business_id, limit=5)

    return {
        "business": business,
        "call_stats": call_stats,
        "recent_calls": recent_calls
    }